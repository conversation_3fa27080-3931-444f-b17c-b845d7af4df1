{"version": 3, "names": ["useMemo", "useState", "Platform", "StyleSheet", "runOnJS", "useAnimatedReaction", "useBottomSheetInternal", "useBottomSheetContentContainerStyle", "enableFooterMarginAdjustment", "_style", "footerHeight", "setFooterHeight", "animatedFooterHeight", "animatedContentHeight", "flattenStyle", "Array", "isArray", "compose", "style", "currentBottomPadding", "paddingBottom", "padding", "paddingVertical", "undefined", "overflow", "get", "result", "previousFooterHeight", "OS", "contentHeight", "set"], "sourceRoot": "../../../src", "sources": ["hooks/useBottomSheetContentContainerStyle.ts"], "mappings": ";;AAAA,SAASA,OAAO,EAAEC,QAAQ,QAAQ,OAAO;AACzC,SACEC,QAAQ,EACRC,UAAU,QAGL,cAAc;AACrB,SAASC,OAAO,EAAEC,mBAAmB,QAAQ,yBAAyB;AACtE,SAASC,sBAAsB,QAAQ,0BAA0B;AAEjE,OAAO,SAASC,mCAAmCA,CACjDC,4BAAqC,EACrCC,MAA2B,EAC3B;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAC,CAAC,CAAC;EACnD;EACA,MAAM;IAAEW,oBAAoB;IAAEC;EAAsB,CAAC,GACnDP,sBAAsB,CAAC,CAAC;EAC1B;;EAEA;EACA,MAAMQ,YAAY,GAAGd,OAAO,CAAY,MAAM;IAC5C,OAAO,CAACS,MAAM,GACV,CAAC,CAAC,GACFM,KAAK,CAACC,OAAO,CAACP,MAAM,CAAC;IACnB;IACCN,UAAU,CAACc,OAAO,CAAC,GAAGR,MAAM,CAAC,GAC7BA,MAAoB;EAC7B,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZ,MAAMS,KAAK,GAAGlB,OAAO,CAAqB,MAAM;IAC9C,IAAI,CAACQ,4BAA4B,EAAE;MACjC,OAAOM,YAAY;IACrB;IAEA,IAAIK,oBAAoB,GAAG,CAAC;IAC5B,IAAIL,YAAY,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;MACpD,MAAM;QAAEM,aAAa;QAAEC,OAAO;QAAEC;MAAgB,CAAC,GAAGR,YAAY;MAChE,IAAIM,aAAa,KAAKG,SAAS,IAAI,OAAOH,aAAa,KAAK,QAAQ,EAAE;QACpED,oBAAoB,GAAGC,aAAa;MACtC,CAAC,MAAM,IACLE,eAAe,KAAKC,SAAS,IAC7B,OAAOD,eAAe,KAAK,QAAQ,EACnC;QACAH,oBAAoB,GAAGG,eAAe;MACxC,CAAC,MAAM,IAAID,OAAO,KAAKE,SAAS,IAAI,OAAOF,OAAO,KAAK,QAAQ,EAAE;QAC/DF,oBAAoB,GAAGE,OAAO;MAChC;IACF;IAEA,OAAO,CACLP,YAAY,EACZ;MACEM,aAAa,EAAED,oBAAoB,GAAGT,YAAY;MAClDc,QAAQ,EAAE;IACZ,CAAC,CACF;EACH,CAAC,EAAE,CAACd,YAAY,EAAEF,4BAA4B,EAAEM,YAAY,CAAC,CAAC;EAC9D;;EAEA;EACAT,mBAAmB,CACjB,MAAMO,oBAAoB,CAACa,GAAG,CAAC,CAAC,EAChC,CAACC,MAAM,EAAEC,oBAAoB,KAAK;IAChC,IAAI,CAACnB,4BAA4B,EAAE;MACjC;IACF;IACAJ,OAAO,CAACO,eAAe,CAAC,CAACe,MAAM,CAAC;IAEhC,IAAIxB,QAAQ,CAAC0B,EAAE,KAAK,KAAK,EAAE;MACzB;AACR;AACA;AACA;AACA;AACA;MACQ,IAAIF,MAAM,IAAI,CAACC,oBAAoB,EAAE;QACnC,MAAME,aAAa,GAAGhB,qBAAqB,CAACY,GAAG,CAAC,CAAC;QACjDZ,qBAAqB,CAACiB,GAAG,CAACD,aAAa,GAAGH,MAAM,CAAC;MACnD;IACF;EACF,CAAC,EACD,CAACd,oBAAoB,EAAEC,qBAAqB,EAAEL,4BAA4B,CAC5E,CAAC;EACD;EACA,OAAOU,KAAK;AACd", "ignoreList": []}