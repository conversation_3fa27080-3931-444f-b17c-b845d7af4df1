{"version": 3, "sources": ["usePortal.ts"], "names": ["usePortal", "hostName", "dispatch", "PortalDispatchContext", "Error", "registerHost", "type", "ACTIONS", "REGISTER_HOST", "deregisterHost", "DEREGISTER_HOST", "addUpdatePortal", "name", "node", "ADD_UPDATE_PORTAL", "portalName", "<PERSON><PERSON><PERSON><PERSON>", "REMOVE_PORTAL", "addPortal", "updatePortal"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AAEO,MAAMA,SAAS,GAAG,CAACC,QAAgB,GAAG,MAApB,KAA+B;AACtD,QAAMC,QAAQ,GAAG,uBAAWC,6BAAX,CAAjB;;AAEA,MAAID,QAAQ,KAAK,IAAjB,EAAuB;AACrB,UAAM,IAAIE,KAAJ,CACJ,4FADI,CAAN;AAGD,GAPqD,CAStD;;;AACA,QAAMC,YAAY,GAAG,wBAAY,MAAM;AACrCH,IAAAA,QAAQ,CAAC;AACPI,MAAAA,IAAI,EAAEC,mBAAQC,aADP;AAEPP,MAAAA,QAAQ,EAAEA;AAFH,KAAD,CAAR,CADqC,CAKrC;AACD,GANoB,EAMlB,EANkB,CAArB;AAQA,QAAMQ,cAAc,GAAG,wBAAY,MAAM;AACvCP,IAAAA,QAAQ,CAAC;AACPI,MAAAA,IAAI,EAAEC,mBAAQG,eADP;AAEPT,MAAAA,QAAQ,EAAEA;AAFH,KAAD,CAAR,CADuC,CAKvC;AACD,GANsB,EAMpB,EANoB,CAAvB;AAQA,QAAMU,eAAe,GAAG,wBAAY,CAACC,IAAD,EAAeC,IAAf,KAAmC;AACrEX,IAAAA,QAAQ,CAAC;AACPI,MAAAA,IAAI,EAAEC,mBAAQO,iBADP;AAEPb,MAAAA,QAFO;AAGPc,MAAAA,UAAU,EAAEH,IAHL;AAIPC,MAAAA;AAJO,KAAD,CAAR,CADqE,CAOrE;AACD,GARuB,EAQrB,EARqB,CAAxB;AAUA,QAAMG,YAAY,GAAG,wBAAaJ,IAAD,IAAkB;AACjDV,IAAAA,QAAQ,CAAC;AACPI,MAAAA,IAAI,EAAEC,mBAAQU,aADP;AAEPhB,MAAAA,QAFO;AAGPc,MAAAA,UAAU,EAAEH;AAHL,KAAD,CAAR,CADiD,CAMjD;AACD,GAPoB,EAOlB,EAPkB,CAArB,CApCsD,CA4CtD;;AAEA,SAAO;AACLP,IAAAA,YADK;AAELI,IAAAA,cAFK;AAGLS,IAAAA,SAAS,EAAEP,eAHN;AAILQ,IAAAA,YAAY,EAAER,eAJT;AAKLK,IAAAA;AALK,GAAP;AAOD,CArDM", "sourcesContent": ["import { ReactNode, useCallback, useContext } from 'react';\nimport { ACTIONS } from '../state/constants';\nimport { PortalDispatchContext } from '../contexts/portal';\n\nexport const usePortal = (hostName: string = 'root') => {\n  const dispatch = useContext(PortalDispatchContext);\n\n  if (dispatch === null) {\n    throw new Error(\n      \"'PortalDispatchContext' cannot be null, please add 'PortalProvider' to the root component.\"\n    );\n  }\n\n  //#region methods\n  const registerHost = useCallback(() => {\n    dispatch({\n      type: ACTIONS.REGISTER_HOST,\n      hostName: hostName,\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  const deregisterHost = useCallback(() => {\n    dispatch({\n      type: ACTIONS.DEREGISTER_HOST,\n      hostName: hostName,\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  const addUpdatePortal = useCallback((name: string, node: ReactNode) => {\n    dispatch({\n      type: ACTIONS.ADD_UPDATE_PORTAL,\n      hostName,\n      portalName: name,\n      node,\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  const removePortal = useCallback((name: string) => {\n    dispatch({\n      type: ACTIONS.REMOVE_PORTAL,\n      hostName,\n      portalName: name,\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  //#endregion\n\n  return {\n    registerHost,\n    deregisterHost,\n    addPortal: addUpdatePortal,\n    updatePortal: addUpdatePortal,\n    removePortal,\n  };\n};\n"]}