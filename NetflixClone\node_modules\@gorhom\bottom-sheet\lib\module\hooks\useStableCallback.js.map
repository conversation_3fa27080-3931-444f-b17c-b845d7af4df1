{"version": 3, "names": ["useCallback", "useEffect", "useLayoutEffect", "useRef", "useStableCallback", "callback", "callback<PERSON><PERSON>", "current", "undefined", "args"], "sourceRoot": "../../../src", "sources": ["hooks/useStableCallback.ts"], "mappings": ";;AAAA,SAASA,WAAW,EAAEC,SAAS,EAAEC,eAAe,EAAEC,MAAM,QAAQ,OAAO;AAIvE;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAC/BC,QAAwB,EACxB;EACA,MAAMC,WAAW,GAAGH,MAAM,CAAiB,CAAC;EAE5CD,eAAe,CAAC,MAAM;IACpBI,WAAW,CAACC,OAAO,GAAGF,QAAQ;EAChC,CAAC,CAAC;EAEFJ,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXK,WAAW,CAACC,OAAO,GAAGC,SAAS;IACjC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OAAOR,WAAW,CAA6B,CAAC,GAAGS,IAAI,KAAK;IAC1D,OAAOH,WAAW,CAACC,OAAO,GAAG,GAAGE,IAAI,CAAC;EACvC,CAAC,EAAE,EAAE,CAAC;AACR", "ignoreList": []}