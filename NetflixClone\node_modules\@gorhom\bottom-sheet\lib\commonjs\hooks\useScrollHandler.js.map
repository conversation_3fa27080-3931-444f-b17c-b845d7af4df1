{"version": 3, "names": ["_reactNativeReanimated", "require", "_utilities", "_useScrollEventsHandlersDefault", "useScrollHandler", "useScrollEventsHandlers", "useScrollEventsHandlersDefault", "onScroll", "onScrollBeginDrag", "onScrollEndDrag", "scrollableRef", "useAnimatedRef", "scrollableContentOffsetY", "useSharedValue", "handleOnScroll", "noop", "handleOnBeginDrag", "handleOnEndDrag", "handleOnMomentumEnd", "handleOnMomentumBegin", "<PERSON><PERSON><PERSON><PERSON>", "useAnimatedScrollHandler", "event", "context", "runOnJS", "nativeEvent", "onBeginDrag", "onEndDrag", "onMomentumBegin", "onMomentumEnd", "exports"], "sourceRoot": "../../../src", "sources": ["hooks/useScrollHandler.ts"], "mappings": ";;;;;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAOA,IAAAC,UAAA,GAAAD,OAAA;AACA,IAAAE,+BAAA,GAAAF,OAAA;AAEO,MAAMG,gBAAgB,GAAGA,CAC9BC,uBAAuB,GAAGC,8DAA8B,EACxDC,QAA0B,EAC1BC,iBAAmC,EACnCC,eAAiC,KAC9B;EACH;EACA,MAAMC,aAAa,GAAG,IAAAC,qCAAc,EAAa,CAAC;;EAElD;EACA,MAAMC,wBAAwB,GAAG,IAAAC,qCAAc,EAAS,CAAC,CAAC;;EAE1D;EACA,MAAM;IACJC,cAAc,GAAGC,sBAAI;IACrBC,iBAAiB,GAAGD,sBAAI;IACxBE,eAAe,GAAGF,sBAAI;IACtBG,mBAAmB,GAAGH,sBAAI;IAC1BI,qBAAqB,GAAGJ;EAC1B,CAAC,GAAGV,uBAAuB,CAACK,aAAa,EAAEE,wBAAwB,CAAC;;EAEpE;EACA,MAAMQ,aAAa,GAAG,IAAAC,+CAAwB,EAC5C;IACEd,QAAQ,EAAEA,CAACe,KAAK,EAAEC,OAAO,KAAK;MAC5BT,cAAc,CAACQ,KAAK,EAAEC,OAAO,CAAC;MAE9B,IAAIhB,QAAQ,EAAE;QACZ,IAAAiB,8BAAO,EAACjB,QAAQ,CAAC,CAAC;UAAEkB,WAAW,EAAEH;QAAM,CAAC,CAAC;MAC3C;IACF,CAAC;IACDI,WAAW,EAAEA,CAACJ,KAAK,EAAEC,OAAO,KAAK;MAC/BP,iBAAiB,CAACM,KAAK,EAAEC,OAAO,CAAC;MAEjC,IAAIf,iBAAiB,EAAE;QACrB,IAAAgB,8BAAO,EAAChB,iBAAiB,CAAC,CAAC;UAAEiB,WAAW,EAAEH;QAAM,CAAC,CAAC;MACpD;IACF,CAAC;IACDK,SAAS,EAAEA,CAACL,KAAK,EAAEC,OAAO,KAAK;MAC7BN,eAAe,CAACK,KAAK,EAAEC,OAAO,CAAC;MAE/B,IAAId,eAAe,EAAE;QACnB,IAAAe,8BAAO,EAACf,eAAe,CAAC,CAAC;UAAEgB,WAAW,EAAEH;QAAM,CAAC,CAAC;MAClD;IACF,CAAC;IACDM,eAAe,EAAET,qBAAqB;IACtCU,aAAa,EAAEX;EACjB,CAAC,EACD,CACEJ,cAAc,EACdE,iBAAiB,EACjBC,eAAe,EACfE,qBAAqB,EACrBD,mBAAmB,EACnBX,QAAQ,EACRC,iBAAiB,EACjBC,eAAe,CAEnB,CAAC;EAED,OAAO;IAAEW,aAAa;IAAEV,aAAa;IAAEE;EAAyB,CAAC;AACnE,CAAC;AAACkB,OAAA,CAAA1B,gBAAA,GAAAA,gBAAA", "ignoreList": []}