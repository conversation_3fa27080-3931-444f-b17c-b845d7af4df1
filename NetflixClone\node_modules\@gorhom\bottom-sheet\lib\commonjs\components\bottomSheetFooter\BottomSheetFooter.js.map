{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_reactNativeReanimated", "_constants", "_hooks", "_utilities", "_styles", "_jsxRuntime", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "BottomSheetFooterComponent", "animatedFooterPosition", "bottomInset", "style", "children", "ref", "useRef", "animatedFooterHeight", "animatedKeyboardState", "useBottomSheetInternal", "containerAnimatedStyle", "useAnimatedStyle", "footerTranslateY", "KEYBOARD_STATE", "SHOWN", "transform", "translateY", "Math", "max", "containerStyle", "useMemo", "styles", "container", "handleContainerLayout", "useCallback", "nativeEvent", "layout", "height", "__DEV__", "print", "component", "method", "category", "params", "handleBoundingClientRect", "useBoundingClientRect", "jsx", "View", "onLayout", "BottomSheetFooter", "exports", "memo", "displayName"], "sourceRoot": "../../../../src", "sources": ["components/bottomSheetFooter/BottomSheetFooter.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,sBAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAKA,IAAAI,UAAA,GAAAJ,OAAA;AACA,IAAAK,OAAA,GAAAL,OAAA;AAAkC,IAAAM,WAAA,GAAAN,OAAA;AAAA,SAAAO,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAT,wBAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAGlC,SAASW,0BAA0BA,CAAC;EAClCC,sBAAsB;EACtBC,WAAW,GAAG,CAAC;EACfC,KAAK;EACLC;AAC6B,CAAC,EAAE;EAChC;EACA,MAAMC,GAAG,GAAG,IAAAC,aAAM,EAAgB,IAAI,CAAC;EACvC;;EAEA;EACA,MAAM;IAAEC,oBAAoB;IAAEC;EAAsB,CAAC,GACnD,IAAAC,6BAAsB,EAAC,CAAC;EAC1B;;EAEA;EACA,MAAMC,sBAAsB,GAAG,IAAAC,uCAAgB,EAAC,MAAM;IACpD,IAAIC,gBAAgB,GAAGX,sBAAsB,CAACb,GAAG,CAAC,CAAC;;IAEnD;AACJ;AACA;IACI,IAAIoB,qBAAqB,CAACpB,GAAG,CAAC,CAAC,KAAKyB,yBAAc,CAACC,KAAK,EAAE;MACxDF,gBAAgB,GAAGA,gBAAgB,GAAGV,WAAW;IACnD;IAEA,OAAO;MACLa,SAAS,EAAE,CACT;QACEC,UAAU,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEN,gBAAgB;MAC1C,CAAC;IAEL,CAAC;EACH,CAAC,EAAE,CAACV,WAAW,EAAEM,qBAAqB,EAAEP,sBAAsB,CAAC,CAAC;EAChE,MAAMkB,cAAc,GAAG,IAAAC,cAAO,EAC5B,MAAM,CAACC,cAAM,CAACC,SAAS,EAAEnB,KAAK,EAAEO,sBAAsB,CAAC,EACvD,CAACP,KAAK,EAAEO,sBAAsB,CAChC,CAAC;EACD;;EAEA;EACA,MAAMa,qBAAqB,GAAG,IAAAC,kBAAW,EACvC,CAAC;IACCC,WAAW,EAAE;MACXC,MAAM,EAAE;QAAEC;MAAO;IACnB;EACiB,CAAC,KAAK;IACvBpB,oBAAoB,CAACR,GAAG,CAAC4B,MAAM,CAAC;IAEhC,IAAIC,OAAO,EAAE;MACX,IAAAC,gBAAK,EAAC;QACJC,SAAS,EAAE,mBAAmB;QAC9BC,MAAM,EAAE,uBAAuB;QAC/BC,QAAQ,EAAE,QAAQ;QAClBC,MAAM,EAAE;UACNN;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EACD,CAACpB,oBAAoB,CACvB,CAAC;EACD,MAAM2B,wBAAwB,GAAG,IAAAV,kBAAW,EAC1C,CAAC;IAAEG;EAA2B,CAAC,KAAK;IAClCpB,oBAAoB,CAACR,GAAG,CAAC4B,MAAM,CAAC;IAEhC,IAAIC,OAAO,EAAE;MACX,IAAAC,gBAAK,EAAC;QACJC,SAAS,EAAE,mBAAmB;QAC9BC,MAAM,EAAE,0BAA0B;QAClCC,QAAQ,EAAE,QAAQ;QAClBC,MAAM,EAAE;UACNN;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EACD,CAACpB,oBAAoB,CACvB,CAAC;EACD;;EAEA;EACA,IAAA4B,4BAAqB,EAAC9B,GAAG,EAAE6B,wBAAwB,CAAC;EACpD;;EAEA,OAAO9B,QAAQ,KAAK,IAAI,gBACtB,IAAAzB,WAAA,CAAAyD,GAAA,EAAC9D,sBAAA,CAAAY,OAAQ,CAACmD,IAAI;IACZhC,GAAG,EAAEA,GAAI;IACTiC,QAAQ,EAAEf,qBAAsB;IAChCpB,KAAK,EAAEgB,cAAe;IAAAf,QAAA,EAErBA;EAAQ,CACI,CAAC,GACd,IAAI;AACV;AAEO,MAAMmC,iBAAiB,GAAAC,OAAA,CAAAD,iBAAA,gBAAG,IAAAE,WAAI,EAACzC,0BAA0B,CAAC;AACjEuC,iBAAiB,CAACG,WAAW,GAAG,mBAAmB", "ignoreList": []}