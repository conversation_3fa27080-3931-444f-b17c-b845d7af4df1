{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_reactNativeGestureHandler", "_reactNativeReanimated", "_constants", "_gesture", "_hooks", "_ScrollableContainer", "_useBottomSheetContentSizeSetter", "_jsxRuntime", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "createBottomSheetScrollableComponent", "type", "ScrollableComponent", "forwardRef", "props", "ref", "focusHook", "scrollEventsHandlersHook", "enableFooterMarginAdjustment", "overScrollMode", "keyboardDismissMode", "showsVerticalScrollIndicator", "contentContainerStyle", "_providedContentContainerStyle", "refreshing", "onRefresh", "progressViewOffset", "refreshControl", "onScroll", "onScrollBeginDrag", "onScrollEndDrag", "onContentSizeChange", "rest", "draggableGesture", "useContext", "BottomSheetDraggableContext", "scrollableRef", "scrollableContentOffsetY", "<PERSON><PERSON><PERSON><PERSON>", "useScrollHandler", "animatedScrollableState", "enableContentPanningGesture", "useBottomSheetInternal", "setContentSize", "useBottomSheetContentSizeSetter", "scrollableAnimatedProps", "useAnimatedProps", "decelerationRate", "SCROLLABLE_DECELERATION_RATE_MAPPER", "value", "SCROLLABLE_STATE", "UNLOCKED", "scrollableGesture", "useMemo", "Gesture", "Native", "simultaneousWithExternalGesture", "shouldCancelWhenOutside", "undefined", "handleContentSizeChange", "useStableCallback", "contentWidth", "contentHeight", "useBottomSheetContentContainerStyle", "useImperativeHandle", "current", "useScrollableSetter", "jsx", "ScrollableContainer", "nativeGesture", "animatedProps", "scrollEventThrottle"], "sourceRoot": "../../../../src", "sources": ["components/bottomSheetScrollable/createBottomSheetScrollableComponent.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AAMA,IAAAC,0BAAA,GAAAD,OAAA;AACA,IAAAE,sBAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AAKA,IAAAI,QAAA,GAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AAOA,IAAAM,oBAAA,GAAAN,OAAA;AACA,IAAAO,gCAAA,GAAAP,OAAA;AAAoF,IAAAQ,WAAA,GAAAR,OAAA;AAAA,SAAAS,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAX,wBAAAW,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAE7E,SAASW,oCAAoCA,CAClDC,IAAqB;AACrB;AACAC,mBAAwB,EACxB;EACA,oBAAO,IAAAC,iBAAU,EAAO,CAACC,KAAK,EAAEC,GAAG,KAAK;IACtC;IACA,MAAM;MACJ;MACAC,SAAS;MACTC,wBAAwB;MACxB;MACAC,4BAA4B,GAAG,KAAK;MACpCC,cAAc,GAAG,OAAO;MACxBC,mBAAmB,GAAG,aAAa;MACnCC,4BAA4B,GAAG,IAAI;MACnCC,qBAAqB,EAAEC,8BAA8B;MACrDC,UAAU;MACVC,SAAS;MACTC,kBAAkB;MAClBC,cAAc;MACd;MACAC,QAAQ;MACRC,iBAAiB;MACjBC,eAAe;MACfC,mBAAmB;MACnB,GAAGC;MACH;IACG,CAAC,GAAGlB,KAAK;IACd;;IAEA;IACA,MAAMmB,gBAAgB,GAAG,IAAAC,iBAAU,EAACC,oCAA2B,CAAC;IAChE,MAAM;MAAEC,aAAa;MAAEC,wBAAwB;MAAEC;IAAc,CAAC,GAC9D,IAAAC,uBAAgB,EACdtB,wBAAwB,EACxBW,QAAQ,EACRC,iBAAiB,EACjBC,eACF,CAAC;IACH,MAAM;MAAEU,uBAAuB;MAAEC;IAA4B,CAAC,GAC5D,IAAAC,6BAAsB,EAAC,CAAC;IAC1B,MAAM;MAAEC;IAAe,CAAC,GAAG,IAAAC,gEAA+B,EAAC,CAAC;IAC5D;;IAEA,IAAI,CAACX,gBAAgB,IAAIQ,2BAA2B,EAAE;MACpD,MAAM,qDAAqD;IAC7D;;IAEA;IACA,MAAMI,uBAAuB,GAAG,IAAAC,uCAAgB,EAC9C,OAAO;MACLC,gBAAgB,EACdC,8CAAmC,CAACR,uBAAuB,CAACS,KAAK,CAAC;MACpE5B,4BAA4B,EAAEA,4BAA4B,GACtDmB,uBAAuB,CAACS,KAAK,KAAKC,2BAAgB,CAACC,QAAQ,GAC3D9B;IACN,CAAC,CAAC,EACF,CAACmB,uBAAuB,EAAEnB,4BAA4B,CACxD,CAAC;IAED,MAAM+B,iBAAiB,GAAG,IAAAC,cAAO,EAC/B,MACEpB,gBAAgB,GACZqB,kCAAO,CAACC,MAAM,CAAC;IACb;IAAA,CACCC,+BAA+B,CAACvB,gBAAgB,CAAC,CACjDwB,uBAAuB,CAAC,KAAK,CAAC,GACjCC,SAAS,EACf,CAACzB,gBAAgB,CACnB,CAAC;IACD;;IAEA;IACA,MAAM0B,uBAAuB,GAAG,IAAAC,wBAAiB,EAC/C,CAACC,YAAoB,EAAEC,aAAqB,KAAK;MAC/CnB,cAAc,CAACmB,aAAa,CAAC;MAC7B,IAAI/B,mBAAmB,EAAE;QACvBA,mBAAmB,CAAC8B,YAAY,EAAEC,aAAa,CAAC;MAClD;IACF,CACF,CAAC;IACD;;IAEA;IACA,MAAMxC,qBAAqB,GAAG,IAAAyC,0CAAmC,EAC/D7C,4BAA4B,EAC5BK,8BACF,CAAC;IACD;;IAEA;IACA;IACA,IAAAyC,0BAAmB,EAACjD,GAAG,EAAE,MAAMqB,aAAa,CAAC6B,OAAO,CAAC;IACrD,IAAAC,0BAAmB,EACjB9B,aAAa,EACbzB,IAAI,EACJ0B,wBAAwB,EACxBZ,SAAS,KAAKiC,SAAS,EACvB1C,SACF,CAAC;IACD;;IAEA;IACA,oBACE,IAAA3B,WAAA,CAAA8E,GAAA,EAAChF,oBAAA,CAAAiF,mBAAmB;MAClBrD,GAAG,EAAEqB,aAAc;MACnBiC,aAAa,EAAEjB,iBAAkB;MACjCkB,aAAa,EAAEzB,uBAAwB;MACvC1B,cAAc,EAAEA,cAAe;MAC/BC,mBAAmB,EAAEA,mBAAoB;MACzCI,UAAU,EAAEA,UAAW;MACvB+C,mBAAmB,EAAE,EAAG;MACxB7C,kBAAkB,EAAEA,kBAAmB;MACvCJ,qBAAqB,EAAEA,qBAAsB;MAC7CG,SAAS,EAAEA,SAAU;MACrBG,QAAQ,EAAEU,aAAc;MACxBP,mBAAmB,EAAE4B,uBAAwB;MAC7ChB,cAAc,EAAEA,cAAe;MAC/B/B,mBAAmB,EAAEA,mBAAoB;MACzCe,cAAc,EAAEA,cAAe;MAAA,GAC3BK;IAAI,CACT,CAAC;IAEJ;EACF,CAAC,CAAC;AACJ", "ignoreList": []}