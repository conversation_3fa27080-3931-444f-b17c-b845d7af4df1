{"version": 3, "names": ["_constants", "require", "DEFAULT_HANDLE_HEIGHT", "exports", "DEFAULT_OVER_DRAG_RESISTANCE_FACTOR", "DEFAULT_ENABLE_CONTENT_PANNING_GESTURE", "DEFAULT_ENABLE_HANDLE_PANNING_GESTURE", "DEFAULT_ENABLE_OVER_DRAG", "DEFAULT_ENABLE_PAN_DOWN_TO_CLOSE", "DEFAULT_ANIMATE_ON_MOUNT", "DEFAULT_DYNAMIC_SIZING", "DEFAULT_KEYBOARD_BEHAVIOR", "KEYBOARD_BEHAVIOR", "interactive", "DEFAULT_KEYBOARD_BLUR_BEHAVIOR", "KEYBOARD_BLUR_BEHAVIOR", "none", "DEFAULT_KEYBOARD_INPUT_MODE", "KEYBOARD_INPUT_MODE", "adjustPan", "DEFAULT_ENABLE_BLUR_KEYBOARD_ON_GESTURE", "INITIAL_VALUE", "Number", "NEGATIVE_INFINITY", "INITIAL_SNAP_POINT", "INITIAL_CONTAINER_HEIGHT", "INITIAL_CONTAINER_OFFSET", "top", "bottom", "left", "right", "INITIAL_HANDLE_HEIGHT", "INITIAL_POSITION", "SCREEN_HEIGHT", "DEFAULT_ACCESSIBLE", "DEFAULT_ACCESSIBILITY_LABEL", "DEFAULT_ACCESSIBILITY_ROLE"], "sourceRoot": "../../../../src", "sources": ["components/bottomSheet/constants.ts"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAOA;AACA,MAAMC,qBAAqB,GAAAC,OAAA,CAAAD,qBAAA,GAAG,EAAE;AAChC,MAAME,mCAAmC,GAAAD,OAAA,CAAAC,mCAAA,GAAG,GAAG;AAC/C,MAAMC,sCAAsC,GAAAF,OAAA,CAAAE,sCAAA,GAAG,IAAI;AACnD,MAAMC,qCAAqC,GAAAH,OAAA,CAAAG,qCAAA,GAAG,IAAI;AAClD,MAAMC,wBAAwB,GAAAJ,OAAA,CAAAI,wBAAA,GAAG,IAAI;AACrC,MAAMC,gCAAgC,GAAAL,OAAA,CAAAK,gCAAA,GAAG,KAAK;AAC9C,MAAMC,wBAAwB,GAAAN,OAAA,CAAAM,wBAAA,GAAG,IAAI;AACrC,MAAMC,sBAAsB,GAAAP,OAAA,CAAAO,sBAAA,GAAG,IAAI;;AAEnC;AACA,MAAMC,yBAAyB,GAAAR,OAAA,CAAAQ,yBAAA,GAAGC,4BAAiB,CAACC,WAAW;AAC/D,MAAMC,8BAA8B,GAAAX,OAAA,CAAAW,8BAAA,GAAGC,iCAAsB,CAACC,IAAI;AAClE,MAAMC,2BAA2B,GAAAd,OAAA,CAAAc,2BAAA,GAAGC,8BAAmB,CAACC,SAAS;AACjE,MAAMC,uCAAuC,GAAAjB,OAAA,CAAAiB,uCAAA,GAAG,KAAK;;AAErD;AACA,MAAMC,aAAa,GAAAlB,OAAA,CAAAkB,aAAA,GAAGC,MAAM,CAACC,iBAAiB;AAC9C,MAAMC,kBAAkB,GAAArB,OAAA,CAAAqB,kBAAA,GAAG,CAAC,GAAG;AAC/B,MAAMC,wBAAwB,GAAAtB,OAAA,CAAAsB,wBAAA,GAAG,CAAC,GAAG;AACrC,MAAMC,wBAAwB,GAAAvB,OAAA,CAAAuB,wBAAA,GAAG;EAC/BC,GAAG,EAAE,CAAC;EACNC,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE;AACT,CAAC;AACD,MAAMC,qBAAqB,GAAA5B,OAAA,CAAA4B,qBAAA,GAAG,CAAC,GAAG;AAClC,MAAMC,gBAAgB,GAAA7B,OAAA,CAAA6B,gBAAA,GAAGC,wBAAa;;AAEtC;AACA,MAAMC,kBAAkB,GAAA/B,OAAA,CAAA+B,kBAAA,GAAG,IAAI;AAC/B,MAAMC,2BAA2B,GAAAhC,OAAA,CAAAgC,2BAAA,GAAG,cAAc;AAClD,MAAMC,0BAA0B,GAAAjC,OAAA,CAAAiC,0BAAA,GAAG,YAAY", "ignoreList": []}