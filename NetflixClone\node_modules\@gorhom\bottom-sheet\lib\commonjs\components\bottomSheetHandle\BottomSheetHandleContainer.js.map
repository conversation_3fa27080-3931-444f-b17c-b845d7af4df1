{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_reactNativeGestureHandler", "_reactNativeReanimated", "_interopRequireDefault", "_hooks", "_utilities", "_constants", "_BottomSheetHandle", "_jsxRuntime", "e", "__esModule", "default", "_getRequireWildcardCache", "WeakMap", "r", "t", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "BottomSheetHandleContainerComponent", "animatedIndex", "animatedPosition", "simultaneousHandlers", "_internalSimultaneousHandlers", "enableHandlePanningGesture", "DEFAULT_ENABLE_HANDLE_PANNING_GESTURE", "handleHeight", "handleComponent", "handleStyle", "_providedHandleStyle", "handleIndicatorStyle", "_providedIndicatorStyle", "ref", "useRef", "activeOffsetX", "activeOffsetY", "failOffsetX", "failOffsetY", "waitFor", "_providedSimultaneousHandlers", "useBottomSheetInternal", "handlePanGestureHandler", "useBottomSheetGestureHandlers", "useMemo", "refs", "push", "Array", "isArray", "panGesture", "gesture", "Gesture", "Pan", "enabled", "shouldCancelWhenOutside", "runOnJS", "onStart", "handleOnStart", "onChange", "handleOnChange", "onEnd", "handleOnEnd", "onFinalize", "handleOnFinalize", "requireExternalGestureToFail", "simultaneousWithExternalGesture", "handleContainerLayout", "useCallback", "nativeEvent", "layout", "height", "value", "__DEV__", "print", "component", "BottomSheetHandleContainer", "displayName", "method", "category", "params", "handleBoundingClientRect", "useBoundingClientRect", "HandleComponent", "BottomSheetHandle", "jsx", "GestureDetector", "children", "View", "onLayout", "style", "indicatorStyle", "memo", "_default", "exports"], "sourceRoot": "../../../../src", "sources": ["components/bottomSheetHandle/BottomSheetHandleContainer.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,0BAAA,GAAAD,OAAA;AACA,IAAAE,sBAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AAMA,IAAAK,UAAA,GAAAL,OAAA;AACA,IAAAM,UAAA,GAAAN,OAAA;AACA,IAAAO,kBAAA,GAAAJ,sBAAA,CAAAH,OAAA;AAAoD,IAAAQ,WAAA,GAAAR,OAAA;AAAA,SAAAG,uBAAAM,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,yBAAAH,CAAA,6BAAAI,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAD,wBAAA,YAAAA,CAAAH,CAAA,WAAAA,CAAA,GAAAM,CAAA,GAAAD,CAAA,KAAAL,CAAA;AAAA,SAAAV,wBAAAU,CAAA,EAAAK,CAAA,SAAAA,CAAA,IAAAL,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAE,OAAA,EAAAF,CAAA,QAAAM,CAAA,GAAAH,wBAAA,CAAAE,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAP,CAAA,UAAAM,CAAA,CAAAE,GAAA,CAAAR,CAAA,OAAAS,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAf,CAAA,oBAAAe,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAe,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAd,CAAA,EAAAe,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAf,CAAA,CAAAe,CAAA,YAAAN,CAAA,CAAAP,OAAA,GAAAF,CAAA,EAAAM,CAAA,IAAAA,CAAA,CAAAa,GAAA,CAAAnB,CAAA,EAAAS,CAAA,GAAAA,CAAA;AAGpD,SAASW,mCAAmCA,CAAC;EAC3CC,aAAa;EACbC,gBAAgB;EAChBC,oBAAoB,EAAEC,6BAA6B;EACnDC,0BAA0B,GAAGC,gDAAqC;EAClEC,YAAY;EACZC,eAAe;EACfC,WAAW,EAAEC,oBAAoB;EACjCC,oBAAoB,EAAEC;AACS,CAAC,EAAE;EAClC;EACA,MAAMC,GAAG,GAAG,IAAAC,aAAM,EAAO,IAAI,CAAC;EAC9B;;EAEA;EACA,MAAM;IACJC,aAAa;IACbC,aAAa;IACbC,WAAW;IACXC,WAAW;IACXC,OAAO;IACPhB,oBAAoB,EAAEiB;EACxB,CAAC,GAAG,IAAAC,6BAAsB,EAAC,CAAC;EAC5B,MAAM;IAAEC;EAAwB,CAAC,GAAG,IAAAC,oCAA6B,EAAC,CAAC;EACnE;;EAEA;EACA,MAAMpB,oBAAoB,GAAG,IAAAqB,cAAO,EAAY,MAAM;IACpD,MAAMC,IAAI,GAAG,EAAE;IAEf,IAAIrB,6BAA6B,EAAE;MACjCqB,IAAI,CAACC,IAAI,CAACtB,6BAA6B,CAAC;IAC1C;IAEA,IAAIgB,6BAA6B,EAAE;MACjC,IAAIO,KAAK,CAACC,OAAO,CAACR,6BAA6B,CAAC,EAAE;QAChDK,IAAI,CAACC,IAAI,CAAC,GAAGN,6BAA6B,CAAC;MAC7C,CAAC,MAAM;QACLK,IAAI,CAACC,IAAI,CAACN,6BAA6B,CAAC;MAC1C;IACF;IAEA,OAAOK,IAAI;EACb,CAAC,EAAE,CAACL,6BAA6B,EAAEhB,6BAA6B,CAAC,CAAC;EAClE,MAAMyB,UAAU,GAAG,IAAAL,cAAO,EAAC,MAAM;IAC/B,IAAIM,OAAO,GAAGC,kCAAO,CAACC,GAAG,CAAC,CAAC,CACxBC,OAAO,CAAC5B,0BAA0B,CAAC,CACnC6B,uBAAuB,CAAC,KAAK,CAAC,CAC9BC,OAAO,CAAC,KAAK,CAAC,CACdC,OAAO,CAACd,uBAAuB,CAACe,aAAa,CAAC,CAC9CC,QAAQ,CAAChB,uBAAuB,CAACiB,cAAc,CAAC,CAChDC,KAAK,CAAClB,uBAAuB,CAACmB,WAAW,CAAC,CAC1CC,UAAU,CAACpB,uBAAuB,CAACqB,gBAAgB,CAAC;IAEvD,IAAIxB,OAAO,EAAE;MACXW,OAAO,GAAGA,OAAO,CAACc,4BAA4B,CAACzB,OAAO,CAAC;IACzD;IAEA,IAAIhB,oBAAoB,EAAE;MACxB2B,OAAO,GAAGA,OAAO,CAACe,+BAA+B,CAC/C1C,oBACF,CAAC;IACH;IAEA,IAAIY,aAAa,EAAE;MACjBe,OAAO,GAAGA,OAAO,CAACf,aAAa,CAACA,aAAa,CAAC;IAChD;IAEA,IAAIC,aAAa,EAAE;MACjBc,OAAO,GAAGA,OAAO,CAACd,aAAa,CAACA,aAAa,CAAC;IAChD;IAEA,IAAIC,WAAW,EAAE;MACfa,OAAO,GAAGA,OAAO,CAACb,WAAW,CAACA,WAAW,CAAC;IAC5C;IAEA,IAAIC,WAAW,EAAE;MACfY,OAAO,GAAGA,OAAO,CAACZ,WAAW,CAACA,WAAW,CAAC;IAC5C;IAEA,OAAOY,OAAO;EAChB,CAAC,EAAE,CACDf,aAAa,EACbC,aAAa,EACbX,0BAA0B,EAC1BY,WAAW,EACXC,WAAW,EACXf,oBAAoB,EACpBgB,OAAO,EACPG,uBAAuB,CAACiB,cAAc,EACtCjB,uBAAuB,CAACmB,WAAW,EACnCnB,uBAAuB,CAACqB,gBAAgB,EACxCrB,uBAAuB,CAACe,aAAa,CACtC,CAAC;EACF;;EAEA;EACA,MAAMS,qBAAqB,GAAG,IAAAC,kBAAW,EACvC,SAASD,qBAAqBA,CAAC;IAC7BE,WAAW,EAAE;MACXC,MAAM,EAAE;QAAEC;MAAO;IACnB;EACiB,CAAC,EAAE;IACpB3C,YAAY,CAAC4C,KAAK,GAAGD,MAAM;IAE3B,IAAIE,OAAO,EAAE;MACX,IAAAC,gBAAK,EAAC;QACJC,SAAS,EAAEC,0BAA0B,CAACC,WAAW;QACjDC,MAAM,EAAE,uBAAuB;QAC/BC,QAAQ,EAAE,QAAQ;QAClBC,MAAM,EAAE;UACNT;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EACD,CAAC3C,YAAY,CACf,CAAC;EACD,MAAMqD,wBAAwB,GAAG,IAAAb,kBAAW,EAC1C,CAAC;IAAEG;EAA2B,CAAC,KAAK;IAClC3C,YAAY,CAAC4C,KAAK,GAAGD,MAAM;IAC3B,IAAIE,OAAO,EAAE;MACX,IAAAC,gBAAK,EAAC;QACJC,SAAS,EAAEC,0BAA0B,CAACC,WAAW;QACjDC,MAAM,EAAE,0BAA0B;QAClCC,QAAQ,EAAE,QAAQ;QAClBC,MAAM,EAAE;UACNT;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EACD,CAAC3C,YAAY,CACf,CAAC;EACD;;EAEA;EACA,IAAAsD,4BAAqB,EAAChD,GAAG,EAAE+C,wBAAwB,CAAC;EACpD;;EAEA;EACA,MAAME,eAAe,GAAGtD,eAAe,IAAIuD,0BAAiB;EAC5D,oBACE,IAAApF,WAAA,CAAAqF,GAAA,EAAC5F,0BAAA,CAAA6F,eAAe;IAACnC,OAAO,EAAED,UAAW;IAAAqC,QAAA,eACnC,IAAAvF,WAAA,CAAAqF,GAAA,EAAC3F,sBAAA,CAAAS,OAAQ,CAACqF,IAAI;MACZtD,GAAG,EAAEA,GAAI;MACTuD,QAAQ,EAAEtB,qBAAsB;MAAAoB,QAAA,eAGhC,IAAAvF,WAAA,CAAAqF,GAAA,EAACF,eAAe;QACd7D,aAAa,EAAEA,aAAc;QAC7BC,gBAAgB,EAAEA,gBAAiB;QACnCmE,KAAK,EAAE3D,oBAAqB;QAC5B4D,cAAc,EAAE1D;MAAwB,CACzC;IAAC,GAPE,4BAQS;EAAC,CACD,CAAC;EAEpB;AACF;AAEA,MAAM2C,0BAA0B,gBAAG,IAAAgB,WAAI,EAACvE,mCAAmC,CAAC;AAC5EuD,0BAA0B,CAACC,WAAW,GAAG,4BAA4B;AAAC,IAAAgB,QAAA,GAAAC,OAAA,CAAA3F,OAAA,GAEvDyE,0BAA0B", "ignoreList": []}