import type { BottomSheetVirtualizedListMethods, BottomSheetVirtualizedListProps } from './types';
declare const BottomSheetVirtualizedList: import("react").MemoExoticComponent<import("react").ForwardRefExoticComponent<Omit<BottomSheetVirtualizedListProps<never>, "ref"> & import("react").RefAttributes<BottomSheetVirtualizedListMethods>>>;
declare const _default: <T>(props: BottomSheetVirtualizedListProps<T>) => ReturnType<typeof BottomSheetVirtualizedList>;
export default _default;
//# sourceMappingURL=BottomSheetVirtualizedList.d.ts.map