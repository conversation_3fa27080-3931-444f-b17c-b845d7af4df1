import { type DefaultSectionT } from 'react-native';
import type { BottomSheetSectionListMethods, BottomSheetSectionListProps } from './types';
declare const BottomSheetSectionList: import("react").MemoExoticComponent<import("react").ForwardRefExoticComponent<Omit<BottomSheetSectionListProps<never, DefaultSectionT>, "ref"> & import("react").RefAttributes<BottomSheetSectionListMethods>>>;
declare const _default: <ItemT, SectionT = DefaultSectionT>(props: BottomSheetSectionListProps<ItemT, SectionT>) => ReturnType<typeof BottomSheetSectionList>;
export default _default;
//# sourceMappingURL=BottomSheetSectionList.d.ts.map