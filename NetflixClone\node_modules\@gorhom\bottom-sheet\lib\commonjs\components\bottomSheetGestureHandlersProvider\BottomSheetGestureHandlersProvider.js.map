{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_reactNativeReanimated", "_constants", "_contexts", "_hooks", "_jsxRuntime", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "BottomSheetGestureHandlersProvider", "gestureEventsHandlersHook", "useGestureEventsHandlers", "useGestureEventsHandlersDefault", "children", "animatedGestureSource", "useSharedValue", "GESTURE_SOURCE", "UNDETERMINED", "animatedContentGestureState", "animatedHandleGestureState", "useBottomSheetInternal", "handleOnStart", "handleOnChange", "handleOnEnd", "handleOnFinalize", "contentPanGestureHandler", "useGestureHandler", "CONTENT", "handlePanGestureHandler", "HANDLE", "contextValue", "useMemo", "jsx", "BottomSheetGestureHandlersContext", "Provider", "value", "_default", "exports"], "sourceRoot": "../../../../src", "sources": ["components/bottomSheetGestureHandlersProvider/BottomSheetGestureHandlersProvider.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,sBAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AAIqB,IAAAK,WAAA,GAAAL,OAAA;AAAA,SAAAM,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAR,wBAAAQ,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAGrB,MAAMW,kCAAkC,GAAGA,CAAC;EAC1CC,yBAAyB,EACvBC,wBAAwB,GAAGC,sCAA+B;EAC5DC;AACuC,CAAC,KAAK;EAC7C;EACA,MAAMC,qBAAqB,GAAG,IAAAC,qCAAc,EAC1CC,yBAAc,CAACC,YACjB,CAAC;EACD;;EAEA;EACA,MAAM;IAAEC,2BAA2B;IAAEC;EAA2B,CAAC,GAC/D,IAAAC,6BAAsB,EAAC,CAAC;EAC1B,MAAM;IAAEC,aAAa;IAAEC,cAAc;IAAEC,WAAW;IAAEC;EAAiB,CAAC,GACpEb,wBAAwB,CAAC,CAAC;EAC5B;;EAEA;EACA,MAAMc,wBAAwB,GAAG,IAAAC,wBAAiB,EAChDV,yBAAc,CAACW,OAAO,EACtBT,2BAA2B,EAC3BJ,qBAAqB,EACrBO,aAAa,EACbC,cAAc,EACdC,WAAW,EACXC,gBACF,CAAC;EAED,MAAMI,uBAAuB,GAAG,IAAAF,wBAAiB,EAC/CV,yBAAc,CAACa,MAAM,EACrBV,0BAA0B,EAC1BL,qBAAqB,EACrBO,aAAa,EACbC,cAAc,EACdC,WAAW,EACXC,gBACF,CAAC;EACD;;EAEA;EACA,MAAMM,YAAY,GAAG,IAAAC,cAAO,EAC1B,OAAO;IACLN,wBAAwB;IACxBG,uBAAuB;IACvBd;EACF,CAAC,CAAC,EACF,CAACW,wBAAwB,EAAEG,uBAAuB,EAAEd,qBAAqB,CAC3E,CAAC;EACD;EACA,oBACE,IAAA1B,WAAA,CAAA4C,GAAA,EAAC9C,SAAA,CAAA+C,iCAAiC,CAACC,QAAQ;IAACC,KAAK,EAAEL,YAAa;IAAAjB,QAAA,EAC7DA;EAAQ,CACiC,CAAC;AAEjD,CAAC;AAAC,IAAAuB,QAAA,GAAAC,OAAA,CAAA1C,OAAA,GAEac,kCAAkC", "ignoreList": []}