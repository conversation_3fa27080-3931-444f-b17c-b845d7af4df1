{"version": 3, "sources": ["logger.ts"], "names": ["isLoggingEnabled", "isDev", "Boolean", "__DEV__", "enableLogging", "console", "warn", "print", "component", "method", "params", "message", "Object", "keys", "map", "key", "join", "log", "filter", "freeze"], "mappings": ";;;;;;AAQA,IAAIA,gBAAgB,GAAG,KAAvB,C,CAEA;;AACA,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAOC,OAAP,KAAmB,WAAnB,IAAkCA,OAAnC,CAArB;;AAEA,MAAMC,aAAa,GAAG,MAAM;AAC1B,MAAI,CAACH,KAAL,EAAY;AACVI,IAAAA,OAAO,CAACC,IAAR,CAAa,kDAAb;AACA;AACD;;AACDN,EAAAA,gBAAgB,GAAG,IAAnB;AACD,CAND;;;;AAQA,IAAIO,KAAY,GAAG,MAAM,CAAE,CAA3B;;;;AAEA,IAAIN,KAAJ,EAAW;AACT,kBAAAM,KAAK,GAAG,CAAC;AAAEC,IAAAA,SAAF;AAAaC,IAAAA,MAAb;AAAqBC,IAAAA;AAArB,GAAD,KAAmC;AACzC,QAAI,CAACV,gBAAL,EAAuB;AACrB;AACD;;AACD,QAAIW,OAAO,GAAG,EAAd;;AAEA,QAAI,OAAOD,MAAP,KAAkB,QAAtB,EAAgC;AAC9BC,MAAAA,OAAO,GAAGC,MAAM,CAACC,IAAP,CAAYH,MAAZ,EACPI,GADO,CACHC,GAAG,cAAOA,GAAP,cAAcL,MAAM,CAACK,GAAD,CAApB,CADA,EAEPC,IAFO,CAEF,GAFE,CAAV;AAGD,KAJD,MAIO;AACLL,MAAAA,OAAO,aAAMD,MAAN,aAAMA,MAAN,cAAMA,MAAN,GAAgB,EAAhB,CAAP;AACD;;AACDL,IAAAA,OAAO,CAACY,GAAR,oBACc,CAACT,SAAD,EAAYC,MAAZ,EAAoBS,MAApB,CAA2BhB,OAA3B,EAAoCc,IAApC,CAAyC,IAAzC,CADd,QAEEL,OAFF;AAID,GAjBD;AAkBD;;AAEDC,MAAM,CAACO,MAAP,CAAcZ,KAAd", "sourcesContent": ["interface PrintOptions {\n  component?: string;\n  method?: string;\n  params?: Record<string, any> | string | number | boolean;\n}\n\ntype Print = (options: PrintOptions) => void;\n\nlet isLoggingEnabled = false;\n\n// __DEV__ global is by default not defined in React Native Web builds\nconst isDev = Boolean(typeof __DEV__ !== 'undefined' && __DEV__)\n\nconst enableLogging = () => {\n  if (!isDev) {\n    console.warn('[Portal] could not enable logging on production!');\n    return;\n  }\n  isLoggingEnabled = true;\n};\n\nlet print: Print = () => {};\n\nif (isDev) {\n  print = ({ component, method, params }) => {\n    if (!isLoggingEnabled) {\n      return;\n    }\n    let message = '';\n\n    if (typeof params === 'object') {\n      message = Object.keys(params)\n        .map(key => `${key}:${params[key]}`)\n        .join(' ');\n    } else {\n      message = `${params ?? ''}`;\n    }\n    console.log(\n      `[Portal::${[component, method].filter(Boolean).join('::')}]`,\n      message\n    );\n  };\n}\n\nObject.freeze(print);\n\nexport { print, enableLogging };\n"]}