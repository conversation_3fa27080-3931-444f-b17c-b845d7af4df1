{"version": 3, "sources": ["portal.ts"], "names": ["createContext", "PortalStateContext", "PortalDispatchContext"], "mappings": "AAAA,SAASA,aAAT,QAA8B,OAA9B;AAIA,OAAO,MAAMC,kBAAkB,gBAAGD,aAAa,CAGrC,IAHqC,CAAxC;AAIP,OAAO,MAAME,qBAAqB,gBAChCF,aAAa,CAAqC,IAArC,CADR", "sourcesContent": ["import { createContext } from 'react';\nimport type { ActionTypes } from '../state/types';\nimport type { PortalType } from '../types';\n\nexport const PortalStateContext = createContext<Record<\n  string,\n  Array<PortalType>\n> | null>(null);\nexport const PortalDispatchContext =\n  createContext<React.Dispatch<ActionTypes> | null>(null);\n"]}