{"version": 3, "names": ["useEffect", "useRef", "cancelAnimation", "makeMutable", "useReactiveSharedValue", "value", "initialValueRef", "valueRef", "current"], "sourceRoot": "../../../src", "sources": ["hooks/useReactiveSharedValue.ts"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAEzC,SAASC,eAAe,EAAEC,WAAW,QAAQ,yBAAyB;AAGtE,OAAO,MAAMC,sBAAsB,GACjCC,KAAQ,IACqC;EAC7C,MAAMC,eAAe,GAAGL,MAAM,CAAI,IAAI,CAAC;EACvC,MAAMM,QAAQ,GAAGN,MAAM,CAAiB,IAAI,CAAC;EAE7C,IAAII,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAO,IAAIA,KAAK,EAAE;IAC1D;AACJ;AACA;AACA;EAHI,CAID,MAAM,IAAIE,QAAQ,CAACC,OAAO,KAAK,IAAI,EAAE;IACpC;IACAF,eAAe,CAACE,OAAO,GAAGH,KAAK;IAC/B;AACJ;AACA;AACA;IACI,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B;MACAE,QAAQ,CAACC,OAAO,GAAGL,WAAW,CAAC;QAAE,GAAGE;MAAM,CAAC,CAAC;IAC9C,CAAC,MAAM;MACL;MACAE,QAAQ,CAACC,OAAO,GAAGL,WAAW,CAACE,KAAK,CAAC;IACvC;EACF,CAAC,MAAM,IAAIC,eAAe,CAACE,OAAO,KAAKH,KAAK,EAAE;IAC5CE,QAAQ,CAACC,OAAO,CAACH,KAAK,GAAGA,KAAU;EACrC;EAEAL,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIO,QAAQ,CAACC,OAAO,EAAE;QACpBN,eAAe,CAACK,QAAQ,CAACC,OAAO,CAAC;MACnC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,OAAOD,QAAQ,CAACC,OAAO,IAAIH,KAAK;AAClC,CAAC", "ignoreList": []}