import React from 'react';
import type { BottomSheetHostingContainerProps } from './types';
declare function BottomSheetHostingContainerComponent({ containerHeight, containerOffset, topInset, bottomInset, shouldCalculateHeight, detached, style, children, }: BottomSheetHostingContainerProps): React.JSX.Element;
export declare const BottomSheetHostingContainer: React.MemoExoticComponent<typeof BottomSheetHostingContainerComponent>;
export {};
//# sourceMappingURL=BottomSheetHostingContainer.d.ts.map