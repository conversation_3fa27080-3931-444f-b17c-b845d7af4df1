{"version": 3, "names": ["_react", "require", "_reactNativeReanimated", "useReactiveSharedValue", "value", "initialValueRef", "useRef", "valueRef", "current", "makeMutable", "useEffect", "cancelAnimation", "exports"], "sourceRoot": "../../../src", "sources": ["hooks/useReactiveSharedValue.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAEA,IAAAC,sBAAA,GAAAD,OAAA;AAGO,MAAME,sBAAsB,GACjCC,KAAQ,IACqC;EAC7C,MAAMC,eAAe,GAAG,IAAAC,aAAM,EAAI,IAAI,CAAC;EACvC,MAAMC,QAAQ,GAAG,IAAAD,aAAM,EAAiB,IAAI,CAAC;EAE7C,IAAIF,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAO,IAAIA,KAAK,EAAE;IAC1D;AACJ;AACA;AACA;EAHI,CAID,MAAM,IAAIG,QAAQ,CAACC,OAAO,KAAK,IAAI,EAAE;IACpC;IACAH,eAAe,CAACG,OAAO,GAAGJ,KAAK;IAC/B;AACJ;AACA;AACA;IACI,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B;MACAG,QAAQ,CAACC,OAAO,GAAG,IAAAC,kCAAW,EAAC;QAAE,GAAGL;MAAM,CAAC,CAAC;IAC9C,CAAC,MAAM;MACL;MACAG,QAAQ,CAACC,OAAO,GAAG,IAAAC,kCAAW,EAACL,KAAK,CAAC;IACvC;EACF,CAAC,MAAM,IAAIC,eAAe,CAACG,OAAO,KAAKJ,KAAK,EAAE;IAC5CG,QAAQ,CAACC,OAAO,CAACJ,KAAK,GAAGA,KAAU;EACrC;EAEA,IAAAM,gBAAS,EAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIH,QAAQ,CAACC,OAAO,EAAE;QACpB,IAAAG,sCAAe,EAACJ,QAAQ,CAACC,OAAO,CAAC;MACnC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,OAAOD,QAAQ,CAACC,OAAO,IAAIJ,KAAK;AAClC,CAAC;AAACQ,OAAA,CAAAT,sBAAA,GAAAA,sBAAA", "ignoreList": []}