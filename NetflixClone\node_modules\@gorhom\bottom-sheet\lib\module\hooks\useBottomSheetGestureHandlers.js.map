{"version": 3, "names": ["useContext", "BottomSheetGestureHandlersContext", "useBottomSheetGestureHandlers", "context"], "sourceRoot": "../../../src", "sources": ["hooks/useBottomSheetGestureHandlers.ts"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,iCAAiC,QAAQ,qBAAqB;AAEvE,OAAO,MAAMC,6BAA6B,GAAGA,CAAA,KAAM;EACjD,MAAMC,OAAO,GAAGH,UAAU,CAACC,iCAAiC,CAAC;EAE7D,IAAIE,OAAO,KAAK,IAAI,EAAE;IACpB,MAAM,wEAAwE;EAChF;EAEA,OAAOA,OAAO;AAChB,CAAC", "ignoreList": []}