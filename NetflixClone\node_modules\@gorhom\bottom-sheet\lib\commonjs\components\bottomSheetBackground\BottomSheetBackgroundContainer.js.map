{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_reactNative", "_BottomSheetBackground", "_styles", "_jsxRuntime", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "BottomSheetBackgroundContainerComponent", "animatedIndex", "animatedPosition", "backgroundComponent", "_providedBackgroundComponent", "backgroundStyle", "_providedBackgroundStyle", "useMemo", "StyleSheet", "flatten", "styles", "container", "BackgroundComponent", "BottomSheetBackground", "jsx", "pointerEvents", "style", "BottomSheetBackgroundContainer", "exports", "memo", "displayName"], "sourceRoot": "../../../../src", "sources": ["components/bottomSheetBackground/BottomSheetBackgroundContainer.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,sBAAA,GAAAF,OAAA;AACA,IAAAG,OAAA,GAAAH,OAAA;AAAkC,IAAAI,WAAA,GAAAJ,OAAA;AAAA,SAAAK,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAP,wBAAAO,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAGlC,MAAMW,uCAAuC,GAAGA,CAAC;EAC/CC,aAAa;EACbC,gBAAgB;EAChBC,mBAAmB,EAAEC,4BAA4B;EACjDC,eAAe,EAAEC;AACkB,CAAC,KAAK;EACzC;EACA,MAAMD,eAAe,GAAG,IAAAE,cAAO,EAC7B,MAAMC,uBAAU,CAACC,OAAO,CAAC,CAACC,cAAM,CAACC,SAAS,EAAEL,wBAAwB,CAAC,CAAC,EACtE,CAACA,wBAAwB,CAC3B,CAAC;EACD;;EAEA,MAAMM,mBAAmB,GACvBR,4BAA4B,IAAIS,4CAAqB;EACvD,oBACE,IAAAlC,WAAA,CAAAmC,GAAA,EAACF,mBAAmB;IAClBG,aAAa,EAAC,MAAM;IACpBd,aAAa,EAAEA,aAAc;IAC7BC,gBAAgB,EAAEA,gBAAiB;IACnCc,KAAK,EAAEX;EAAgB,CACxB,CAAC;AAEN,CAAC;AAEM,MAAMY,8BAA8B,GAAAC,OAAA,CAAAD,8BAAA,gBAAG,IAAAE,WAAI,EAChDnB,uCACF,CAAC;AACDiB,8BAA8B,CAACG,WAAW,GAAG,gCAAgC", "ignoreList": []}