{"version": 3, "sources": ["usePortalState.ts"], "names": ["usePortalState", "hostName", "state", "PortalStateContext", "Error"], "mappings": ";;;;;;;AAAA;;AACA;;AAEO,MAAMA,cAAc,GAAIC,QAAD,IAAsB;AAClD,QAAMC,KAAK,GAAG,uBAAWC,0BAAX,CAAd;;AAEA,MAAID,KAAK,KAAK,IAAd,EAAoB;AAClB,UAAM,IAAIE,KAAJ,CACJ,yFADI,CAAN;AAGD;;AAED,SAAOF,KAAK,CAACD,QAAD,CAAL,IAAmB,EAA1B;AACD,CAVM", "sourcesContent": ["import { useContext } from 'react';\nimport { PortalStateContext } from '../contexts/portal';\n\nexport const usePortalState = (hostName: string) => {\n  const state = useContext(PortalStateContext);\n\n  if (state === null) {\n    throw new Error(\n      \"'PortalStateContext' cannot be null, please add 'PortalProvider' to the root component.\"\n    );\n  }\n\n  return state[hostName] || [];\n};\n"]}