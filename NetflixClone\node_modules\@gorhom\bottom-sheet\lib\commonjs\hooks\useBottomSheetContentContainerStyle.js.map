{"version": 3, "names": ["_react", "require", "_reactNative", "_reactNativeReanimated", "_useBottomSheetInternal", "useBottomSheetContentContainerStyle", "enableFooterMarginAdjustment", "_style", "footerHeight", "setFooterHeight", "useState", "animatedFooterHeight", "animatedContentHeight", "useBottomSheetInternal", "flattenStyle", "useMemo", "Array", "isArray", "StyleSheet", "compose", "style", "currentBottomPadding", "paddingBottom", "padding", "paddingVertical", "undefined", "overflow", "useAnimatedReaction", "get", "result", "previousFooterHeight", "runOnJS", "Platform", "OS", "contentHeight", "set"], "sourceRoot": "../../../src", "sources": ["hooks/useBottomSheetContentContainerStyle.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAMA,IAAAE,sBAAA,GAAAF,OAAA;AACA,IAAAG,uBAAA,GAAAH,OAAA;AAEO,SAASI,mCAAmCA,CACjDC,4BAAqC,EACrCC,MAA2B,EAC3B;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG,IAAAC,eAAQ,EAAC,CAAC,CAAC;EACnD;EACA,MAAM;IAAEC,oBAAoB;IAAEC;EAAsB,CAAC,GACnD,IAAAC,8CAAsB,EAAC,CAAC;EAC1B;;EAEA;EACA,MAAMC,YAAY,GAAG,IAAAC,cAAO,EAAY,MAAM;IAC5C,OAAO,CAACR,MAAM,GACV,CAAC,CAAC,GACFS,KAAK,CAACC,OAAO,CAACV,MAAM,CAAC;IACnB;IACCW,uBAAU,CAACC,OAAO,CAAC,GAAGZ,MAAM,CAAC,GAC7BA,MAAoB;EAC7B,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZ,MAAMa,KAAK,GAAG,IAAAL,cAAO,EAAqB,MAAM;IAC9C,IAAI,CAACT,4BAA4B,EAAE;MACjC,OAAOQ,YAAY;IACrB;IAEA,IAAIO,oBAAoB,GAAG,CAAC;IAC5B,IAAIP,YAAY,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;MACpD,MAAM;QAAEQ,aAAa;QAAEC,OAAO;QAAEC;MAAgB,CAAC,GAAGV,YAAY;MAChE,IAAIQ,aAAa,KAAKG,SAAS,IAAI,OAAOH,aAAa,KAAK,QAAQ,EAAE;QACpED,oBAAoB,GAAGC,aAAa;MACtC,CAAC,MAAM,IACLE,eAAe,KAAKC,SAAS,IAC7B,OAAOD,eAAe,KAAK,QAAQ,EACnC;QACAH,oBAAoB,GAAGG,eAAe;MACxC,CAAC,MAAM,IAAID,OAAO,KAAKE,SAAS,IAAI,OAAOF,OAAO,KAAK,QAAQ,EAAE;QAC/DF,oBAAoB,GAAGE,OAAO;MAChC;IACF;IAEA,OAAO,CACLT,YAAY,EACZ;MACEQ,aAAa,EAAED,oBAAoB,GAAGb,YAAY;MAClDkB,QAAQ,EAAE;IACZ,CAAC,CACF;EACH,CAAC,EAAE,CAAClB,YAAY,EAAEF,4BAA4B,EAAEQ,YAAY,CAAC,CAAC;EAC9D;;EAEA;EACA,IAAAa,0CAAmB,EACjB,MAAMhB,oBAAoB,CAACiB,GAAG,CAAC,CAAC,EAChC,CAACC,MAAM,EAAEC,oBAAoB,KAAK;IAChC,IAAI,CAACxB,4BAA4B,EAAE;MACjC;IACF;IACA,IAAAyB,8BAAO,EAACtB,eAAe,CAAC,CAACoB,MAAM,CAAC;IAEhC,IAAIG,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;MACzB;AACR;AACA;AACA;AACA;AACA;MACQ,IAAIJ,MAAM,IAAI,CAACC,oBAAoB,EAAE;QACnC,MAAMI,aAAa,GAAGtB,qBAAqB,CAACgB,GAAG,CAAC,CAAC;QACjDhB,qBAAqB,CAACuB,GAAG,CAACD,aAAa,GAAGL,MAAM,CAAC;MACnD;IACF;EACF,CAAC,EACD,CAAClB,oBAAoB,EAAEC,qBAAqB,EAAEN,4BAA4B,CAC5E,CAAC;EACD;EACA,OAAOc,KAAK;AACd", "ignoreList": []}