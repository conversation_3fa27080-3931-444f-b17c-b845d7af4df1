{"version": 3, "names": ["_portal", "require", "_react", "_interopRequireWildcard", "_reactNativeReanimated", "_constants", "_contexts", "_id", "_constants2", "_bottomSheetHostingContainer", "_jsxRuntime", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "BottomSheetModalProviderWrapper", "children", "animatedContainerHeight", "useSharedValue", "INITIAL_CONTAINER_HEIGHT", "animatedContainerOffset", "INITIAL_CONTAINER_OFFSET", "hostName", "useMemo", "id", "sheetsQueueRef", "useRef", "handleMountSheet", "useCallback", "key", "ref", "stack<PERSON>eh<PERSON>or", "_sheetsQueue", "current", "slice", "sheetIndex", "findIndex", "item", "sheetOnTop", "length", "currentMountedSheet", "will<PERSON>n<PERSON>", "MODAL_STACK_BEHAVIOR", "replace", "dismiss", "switch", "minimize", "splice", "restore", "push", "handleUnmountSheet", "hasMinimizedSheet", "minimizedSheet", "handleWillUnmountSheet", "handle<PERSON><PERSON><PERSON>", "sheetToBeDismissed", "find", "handleDismissAll", "map", "externalContextVariables", "dismissAll", "internalContextVariables", "containerHeight", "containerOffset", "mountSheet", "unmountSheet", "willUnmountSheet", "jsx", "BottomSheetModalProvider", "value", "jsxs", "BottomSheetModalInternalProvider", "BottomSheetHostingContainer", "PortalProvider", "rootHostName", "_default", "exports"], "sourceRoot": "../../../../src", "sources": ["components/bottomSheetModalProvider/BottomSheetModalProvider.tsx"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,sBAAA,GAAAH,OAAA;AACA,IAAAI,UAAA,GAAAJ,OAAA;AACA,IAAAK,SAAA,GAAAL,OAAA;AAIA,IAAAM,GAAA,GAAAN,OAAA;AACA,IAAAO,WAAA,GAAAP,OAAA;AAIA,IAAAQ,4BAAA,GAAAR,OAAA;AAA6E,IAAAS,WAAA,GAAAT,OAAA;AAAA,SAAAU,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAT,wBAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAU7E,MAAMW,+BAA+B,GAAGA,CAAC;EACvCC;AAC6B,CAAC,KAAK;EACnC;EACA,MAAMC,uBAAuB,GAAG,IAAAC,qCAAc,EAACC,oCAAwB,CAAC;EACxE,MAAMC,uBAAuB,GAAG,IAAAF,qCAAc,EAACG,oCAAwB,CAAC;EACxE;;EAEA;EACA,MAAMC,QAAQ,GAAG,IAAAC,cAAO,EAAC,MAAM,uBAAuB,IAAAC,MAAE,EAAC,CAAC,EAAE,EAAE,EAAE,CAAC;EACjE,MAAMC,cAAc,GAAG,IAAAC,aAAM,EAAwB,EAAE,CAAC;EACxD;;EAEA;EACA,MAAMC,gBAAgB,GAAG,IAAAC,kBAAW,EAClC,CACEC,GAAW,EACXC,GAAoD,EACpDC,aAA4C,KACzC;IACH,MAAMC,YAAY,GAAGP,cAAc,CAACQ,OAAO,CAACC,KAAK,CAAC,CAAC;IACnD,MAAMC,UAAU,GAAGH,YAAY,CAACI,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACR,GAAG,KAAKA,GAAG,CAAC;IACnE,MAAMS,UAAU,GAAGH,UAAU,KAAKH,YAAY,CAACO,MAAM,GAAG,CAAC;;IAEzD;AACN;AACA;AACA;IACM,IAAIJ,UAAU,KAAK,CAAC,CAAC,IAAIG,UAAU,EAAE;MACnC;IACF;;IAEA;AACN;AACA;AACA;AACA;AACA;;IAEM;AACN;AACA;AACA;AACA;IACM,MAAME,mBAAmB,GAAGR,YAAY,CAACA,YAAY,CAACO,MAAM,GAAG,CAAC,CAAC;IACjE,IAAIC,mBAAmB,IAAI,CAACA,mBAAmB,CAACC,WAAW,EAAE;MAC3D,IAAIV,aAAa,KAAKW,+BAAoB,CAACC,OAAO,EAAE;QAClDH,mBAAmB,CAACV,GAAG,EAAEG,OAAO,EAAEW,OAAO,CAAC,CAAC;MAC7C,CAAC,MAAM,IAAIb,aAAa,KAAKW,+BAAoB,CAACG,MAAM,EAAE;QACxDL,mBAAmB,CAACV,GAAG,EAAEG,OAAO,EAAEa,QAAQ,CAAC,CAAC;MAC9C;IACF;;IAEA;AACN;AACA;AACA;IACM,IAAIX,UAAU,KAAK,CAAC,CAAC,EAAE;MACrBH,YAAY,CAACe,MAAM,CAACZ,UAAU,EAAE,CAAC,CAAC;MAClCL,GAAG,EAAEG,OAAO,EAAEe,OAAO,CAAC,CAAC;IACzB;IAEAhB,YAAY,CAACiB,IAAI,CAAC;MAChBpB,GAAG;MACHC,GAAG;MACHW,WAAW,EAAE;IACf,CAAC,CAAC;IACFhB,cAAc,CAACQ,OAAO,GAAGD,YAAY;EACvC,CAAC,EACD,EACF,CAAC;EACD,MAAMkB,kBAAkB,GAAG,IAAAtB,kBAAW,EAAEC,GAAW,IAAK;IACtD,MAAMG,YAAY,GAAGP,cAAc,CAACQ,OAAO,CAACC,KAAK,CAAC,CAAC;IACnD,MAAMC,UAAU,GAAGH,YAAY,CAACI,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACR,GAAG,KAAKA,GAAG,CAAC;IACnE,MAAMS,UAAU,GAAGH,UAAU,KAAKH,YAAY,CAACO,MAAM,GAAG,CAAC;;IAEzD;AACJ;AACA;AACA;IACIP,YAAY,CAACe,MAAM,CAACZ,UAAU,EAAE,CAAC,CAAC;IAClCV,cAAc,CAACQ,OAAO,GAAGD,YAAY;;IAErC;AACJ;AACA;AACA;AACA;IACI,MAAMmB,iBAAiB,GAAG1B,cAAc,CAACQ,OAAO,CAACM,MAAM,GAAG,CAAC;IAC3D,MAAMa,cAAc,GAClB3B,cAAc,CAACQ,OAAO,CAACR,cAAc,CAACQ,OAAO,CAACM,MAAM,GAAG,CAAC,CAAC;IAC3D,IACED,UAAU,IACVa,iBAAiB,IACjBC,cAAc,IACd,CAACA,cAAc,CAACX,WAAW,EAC3B;MACAhB,cAAc,CAACQ,OAAO,CACpBR,cAAc,CAACQ,OAAO,CAACM,MAAM,GAAG,CAAC,CAClC,CAACT,GAAG,EAAEG,OAAO,EAAEe,OAAO,CAAC,CAAC;IAC3B;EACF,CAAC,EAAE,EAAE,CAAC;EACN,MAAMK,sBAAsB,GAAG,IAAAzB,kBAAW,EAAEC,GAAW,IAAK;IAC1D,MAAMG,YAAY,GAAGP,cAAc,CAACQ,OAAO,CAACC,KAAK,CAAC,CAAC;IACnD,MAAMC,UAAU,GAAGH,YAAY,CAACI,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACR,GAAG,KAAKA,GAAG,CAAC;IACnE,MAAMS,UAAU,GAAGH,UAAU,KAAKH,YAAY,CAACO,MAAM,GAAG,CAAC;;IAEzD;AACJ;AACA;AACA;IACI,IAAIJ,UAAU,KAAK,CAAC,CAAC,EAAE;MACrBH,YAAY,CAACG,UAAU,CAAC,CAACM,WAAW,GAAG,IAAI;IAC7C;;IAEA;AACJ;AACA;AACA;IACI,MAAMU,iBAAiB,GAAGnB,YAAY,CAACO,MAAM,GAAG,CAAC;IACjD,IAAID,UAAU,IAAIa,iBAAiB,EAAE;MACnCnB,YAAY,CAACA,YAAY,CAACO,MAAM,GAAG,CAAC,CAAC,CAACT,GAAG,EAAEG,OAAO,EAAEe,OAAO,CAAC,CAAC;IAC/D;IAEAvB,cAAc,CAACQ,OAAO,GAAGD,YAAY;EACvC,CAAC,EAAE,EAAE,CAAC;EACN;;EAEA;EACA,MAAMsB,aAAa,GAAG,IAAA1B,kBAAW,EAAEC,GAAY,IAAK;IAClD,MAAM0B,kBAAkB,GAAG1B,GAAG,GAC1BJ,cAAc,CAACQ,OAAO,CAACuB,IAAI,CAACnB,IAAI,IAAIA,IAAI,CAACR,GAAG,KAAKA,GAAG,CAAC,GACrDJ,cAAc,CAACQ,OAAO,CAACR,cAAc,CAACQ,OAAO,CAACM,MAAM,GAAG,CAAC,CAAC;IAC7D,IAAIgB,kBAAkB,EAAE;MACtBA,kBAAkB,CAACzB,GAAG,EAAEG,OAAO,EAAEW,OAAO,CAAC,CAAC;MAC1C,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,EAAE,EAAE,CAAC;EACN,MAAMa,gBAAgB,GAAG,IAAA7B,kBAAW,EAAC,MAAM;IACzCH,cAAc,CAACQ,OAAO,CAACyB,GAAG,CAACrB,IAAI,IAAI;MACjCA,IAAI,CAACP,GAAG,EAAEG,OAAO,EAAEW,OAAO,CAAC,CAAC;IAC9B,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN;;EAEA;EACA,MAAMe,wBAAwB,GAAG,IAAApC,cAAO,EACtC,OAAO;IACLqB,OAAO,EAAEU,aAAa;IACtBM,UAAU,EAAEH;EACd,CAAC,CAAC,EACF,CAACH,aAAa,EAAEG,gBAAgB,CAClC,CAAC;EACD,MAAMI,wBAAwB,GAAG,IAAAtC,cAAO,EACtC,OAAO;IACLD,QAAQ;IACRwC,eAAe,EAAE7C,uBAAuB;IACxC8C,eAAe,EAAE3C,uBAAuB;IACxC4C,UAAU,EAAErC,gBAAgB;IAC5BsC,YAAY,EAAEf,kBAAkB;IAChCgB,gBAAgB,EAAEb;EACpB,CAAC,CAAC,EACF,CACE/B,QAAQ,EACRL,uBAAuB,EACvBG,uBAAuB,EACvBO,gBAAgB,EAChBuB,kBAAkB,EAClBG,sBAAsB,CAE1B,CAAC;EACD;;EAEA;EACA,oBACE,IAAA3D,WAAA,CAAAyE,GAAA,EAAC7E,SAAA,CAAA8E,wBAAwB;IAACC,KAAK,EAAEV,wBAAyB;IAAA3C,QAAA,eACxD,IAAAtB,WAAA,CAAA4E,IAAA,EAAChF,SAAA,CAAAiF,gCAAgC;MAACF,KAAK,EAAER,wBAAyB;MAAA7C,QAAA,gBAChE,IAAAtB,WAAA,CAAAyE,GAAA,EAAC1E,4BAAA,CAAA+E,2BAA2B;QAC1BT,eAAe,EAAE3C,uBAAwB;QACzC0C,eAAe,EAAE7C;MAAwB,CAC1C,CAAC,eACF,IAAAvB,WAAA,CAAAyE,GAAA,EAACnF,OAAA,CAAAyF,cAAc;QAACC,YAAY,EAAEpD,QAAS;QAAAN,QAAA,EAAEA;MAAQ,CAAiB,CAAC;IAAA,CACnC;EAAC,CACX,CAAC;EAE7B;AACF,CAAC;AAAC,IAAA2D,QAAA,GAAAC,OAAA,CAAA3E,OAAA,GAEac,+BAA+B", "ignoreList": []}