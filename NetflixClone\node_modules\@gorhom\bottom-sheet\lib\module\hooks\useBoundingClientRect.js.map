{"version": 3, "names": ["useLayoutEffect", "isFabricInstalled", "useBoundingClientRect", "ref", "handler", "current", "unstable_getBoundingClientRect", "layout", "getBoundingClientRect"], "sourceRoot": "../../../src", "sources": ["hooks/useBoundingClientRect.ts"], "mappings": ";;AAAA,SAAyBA,eAAe,QAAQ,OAAO;AAEvD,SAASC,iBAAiB,QAAQ,gCAAgC;AAalE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CACnCC,GAA2B,EAC3BC,OAA6C,EAC7C;EACA,IAAI,CAACH,iBAAiB,CAAC,CAAC,EAAE;IACxB;EACF;;EAEA;EACAD,eAAe,CAAC,MAAM;IACpB,IAAI,CAACG,GAAG,IAAI,CAACA,GAAG,CAACE,OAAO,EAAE;MACxB;IACF;;IAEA;IACA,IAAIF,GAAG,CAACE,OAAO,CAACC,8BAA8B,KAAK,IAAI,EAAE;MACvD;MACA,MAAMC,MAAM,GAAGJ,GAAG,CAACE,OAAO,CAACC,8BAA8B,CAAC,CAAC;MAC3DF,OAAO,CAACG,MAAM,CAAC;MACf;IACF;;IAEA;IACA,IAAIJ,GAAG,CAACE,OAAO,CAACG,qBAAqB,KAAK,IAAI,EAAE;MAC9C;MACA,MAAMD,MAAM,GAAGJ,GAAG,CAACE,OAAO,CAACG,qBAAqB,CAAC,CAAC;MAClDJ,OAAO,CAACG,MAAM,CAAC;IACjB;EACF,CAAC,CAAC;AACJ", "ignoreList": []}