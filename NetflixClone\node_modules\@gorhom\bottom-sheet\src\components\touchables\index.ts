import type {
  TouchableHighlight as <PERSON>NT<PERSON><PERSON><PERSON><PERSON>ighlight,
  TouchableOpacity as RNTou<PERSON><PERSON><PERSON><PERSON>city,
  TouchableWithoutFeedback as RNTouchableWithoutFeedback,
} from 'react-native';

import {
  TouchableHighlight,
  TouchableOpacity,
  TouchableWithoutFeedback,
  // @ts-ignore
} from './Touchables';

export default {
  TouchableOpacity: TouchableOpacity as never as typeof RNTouchableOpacity,
  TouchableHighlight:
    TouchableHighlight as never as typeof RNTouchableHighlight,
  TouchableWithoutFeedback:
    TouchableWithoutFeedback as never as typeof RNTouchableWithoutFeedback,
};
