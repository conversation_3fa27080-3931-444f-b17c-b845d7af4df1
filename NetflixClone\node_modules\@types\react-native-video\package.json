{"name": "@types/react-native-video", "version": "5.0.20", "description": "TypeScript definitions for react-native-video", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-native-video", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "huhuanming", "url": "https://github.com/huhuanming"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Nekith"}, {"name": "<PERSON>", "githubUsername": "bananer", "url": "https://github.com/bananer"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-native-video"}, "scripts": {}, "dependencies": {"@types/react": "*", "react-native": "*"}, "typesPublisherContentHash": "49a90d104d114801dcc123e615d531361c7c3fc886914c0450a0a87773a5a630", "typeScriptVersion": "4.7"}