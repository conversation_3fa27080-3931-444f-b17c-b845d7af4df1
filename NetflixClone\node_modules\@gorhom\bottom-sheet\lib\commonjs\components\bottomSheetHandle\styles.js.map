{"version": 3, "names": ["_reactNative", "require", "_constants", "styles", "exports", "StyleSheet", "create", "container", "padding", "cursor", "indicator", "alignSelf", "width", "WINDOW_WIDTH", "height", "borderRadius", "backgroundColor"], "sourceRoot": "../../../../src", "sources": ["components/bottomSheetHandle/styles.ts"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;AAEO,MAAME,MAAM,GAAAC,OAAA,CAAAD,MAAA,GAAGE,uBAAU,CAACC,MAAM,CAAC;EACtCC,SAAS,EAAE;IACTC,OAAO,EAAE,EAAE;IACX;IACAC,MAAM,EAAE;EACV,CAAC;EAEDC,SAAS,EAAE;IACTC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAG,GAAG,GAAGC,uBAAY,GAAI,GAAG;IACjCC,MAAM,EAAE,CAAC;IACTC,YAAY,EAAE,CAAC;IACfC,eAAe,EAAE;EACnB;AACF,CAAC,CAAC", "ignoreList": []}