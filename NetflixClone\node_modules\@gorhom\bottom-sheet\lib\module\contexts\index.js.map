{"version": 3, "names": ["BottomSheetContext", "BottomSheetProvider", "BottomSheetInternalContext", "BottomSheetInternalProvider", "BottomSheetGestureHandlersContext", "BottomSheetModalContext", "BottomSheetModalProvider", "BottomSheetModalInternalContext", "BottomSheetModalInternalProvider"], "sourceRoot": "../../../src", "sources": ["contexts/index.ts"], "mappings": ";;AAAA,SAASA,kBAAkB,EAAEC,mBAAmB,QAAQ,YAAY;AACpE,SACEC,0BAA0B,EAC1BC,2BAA2B,QACtB,YAAY;AACnB,SAASC,iCAAiC,QAAQ,WAAW;AAC7D,SACEC,uBAAuB,EACvBC,wBAAwB,QACnB,kBAAkB;AACzB,SACEC,+BAA+B,EAC/BC,gCAAgC,QAC3B,kBAAkB", "ignoreList": []}