{"version": 3, "names": ["_react", "require", "_reactNative", "_reactNativeReanimated", "_constants", "KEYBOARD_EVENT_MAPPER", "KEYBOARD_SHOW", "Platform", "select", "ios", "android", "default", "KEYBOARD_HIDE", "useKeyboard", "shouldHandleKeyboardEvents", "useSharedValue", "keyboardState", "KEYBOARD_STATE", "UNDETERMINED", "keyboardHeight", "keyboardAnimationEasing", "keyboardAnimationDuration", "temporaryCachedKeyboardEvent", "handleKeyboardEvent", "useWorkletCallback", "state", "height", "duration", "easing", "bottomOffset", "SHOWN", "value", "useEffect", "handleOnKeyboardShow", "event", "runOnUI", "endCoordinates", "SCREEN_HEIGHT", "screenY", "handleOnKeyboardHide", "HIDDEN", "showSubscription", "Keyboard", "addListener", "hideSubscription", "remove", "useAnimatedReaction", "result", "params", "length", "animationEasing", "animationDuration", "exports"], "sourceRoot": "../../../src", "sources": ["hooks/useKeyboard.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAOA,IAAAE,sBAAA,GAAAF,OAAA;AAMA,IAAAG,UAAA,GAAAH,OAAA;AAEA,MAAMI,qBAAqB,GAAG;EAC5BC,aAAa,EAAEC,qBAAQ,CAACC,MAAM,CAAC;IAC7BC,GAAG,EAAE,kBAAkB;IACvBC,OAAO,EAAE,iBAAiB;IAC1BC,OAAO,EAAE;EACX,CAAC,CAAsB;EACvBC,aAAa,EAAEL,qBAAQ,CAACC,MAAM,CAAC;IAC7BC,GAAG,EAAE,kBAAkB;IACvBC,OAAO,EAAE,iBAAiB;IAC1BC,OAAO,EAAE;EACX,CAAC;AACH,CAAC;AAEM,MAAME,WAAW,GAAGA,CAAA,KAAM;EAC/B;EACA,MAAMC,0BAA0B,GAAG,IAAAC,qCAAc,EAAC,KAAK,CAAC;EACxD,MAAMC,aAAa,GAAG,IAAAD,qCAAc,EAClCE,yBAAc,CAACC,YACjB,CAAC;EACD,MAAMC,cAAc,GAAG,IAAAJ,qCAAc,EAAC,CAAC,CAAC;EACxC,MAAMK,uBAAuB,GAC3B,IAAAL,qCAAc,EAAsB,UAAU,CAAC;EACjD,MAAMM,yBAAyB,GAAG,IAAAN,qCAAc,EAAC,GAAG,CAAC;EACrD;EACA,MAAMO,4BAA4B,GAAG,IAAAP,qCAAc,EAAQ,EAAE,CAAC;EAC9D;;EAEA;EACA,MAAMQ,mBAAmB,GAAG,IAAAC,yCAAkB,EAC5C,CACEC,KAAqB,EACrBC,MAAc,EACdC,QAAgB,EAChBC,MAA2B,EAC3BC,YAAqB,KAClB;IACH,IAAIJ,KAAK,KAAKR,yBAAc,CAACa,KAAK,IAAI,CAAChB,0BAA0B,CAACiB,KAAK,EAAE;MACvE;AACR;AACA;AACA;AACA;MACQT,4BAA4B,CAACS,KAAK,GAAG,CAACN,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,CAAC;MACtE;IACF;IACAT,cAAc,CAACY,KAAK,GAClBN,KAAK,KAAKR,yBAAc,CAACa,KAAK,GAAGJ,MAAM,GAAGP,cAAc,CAACY,KAAK;;IAEhE;AACN;AACA;AACA;IACM,IAAIF,YAAY,EAAE;MAChBV,cAAc,CAACY,KAAK,GAAGZ,cAAc,CAACY,KAAK,GAAGF,YAAY;IAC5D;IAEAR,yBAAyB,CAACU,KAAK,GAAGJ,QAAQ;IAC1CP,uBAAuB,CAACW,KAAK,GAAGH,MAAM;IACtCZ,aAAa,CAACe,KAAK,GAAGN,KAAK;IAC3BH,4BAA4B,CAACS,KAAK,GAAG,EAAE;EACzC,CAAC,EACD,EACF,CAAC;EACD;;EAEA;EACA,IAAAC,gBAAS,EAAC,MAAM;IACd,MAAMC,oBAAoB,GAAIC,KAAoB,IAAK;MACrD,IAAAC,8BAAO,EAACZ,mBAAmB,CAAC,CAC1BN,yBAAc,CAACa,KAAK,EACpBI,KAAK,CAACE,cAAc,CAACV,MAAM,EAC3BQ,KAAK,CAACP,QAAQ,EACdO,KAAK,CAACN,MAAM,EACZS,wBAAa,GACXH,KAAK,CAACE,cAAc,CAACV,MAAM,GAC3BQ,KAAK,CAACE,cAAc,CAACE,OACzB,CAAC;IACH,CAAC;IACD,MAAMC,oBAAoB,GAAIL,KAAoB,IAAK;MACrD,IAAAC,8BAAO,EAACZ,mBAAmB,CAAC,CAC1BN,yBAAc,CAACuB,MAAM,EACrBN,KAAK,CAACE,cAAc,CAACV,MAAM,EAC3BQ,KAAK,CAACP,QAAQ,EACdO,KAAK,CAACN,MACR,CAAC;IACH,CAAC;IAED,MAAMa,gBAAgB,GAAGC,qBAAQ,CAACC,WAAW,CAC3CtC,qBAAqB,CAACC,aAAa,EACnC2B,oBACF,CAAC;IAED,MAAMW,gBAAgB,GAAGF,qBAAQ,CAACC,WAAW,CAC3CtC,qBAAqB,CAACO,aAAa,EACnC2B,oBACF,CAAC;IAED,OAAO,MAAM;MACXE,gBAAgB,CAACI,MAAM,CAAC,CAAC;MACzBD,gBAAgB,CAACC,MAAM,CAAC,CAAC;IAC3B,CAAC;EACH,CAAC,EAAE,CAACtB,mBAAmB,CAAC,CAAC;;EAEzB;AACF;AACA;AACA;AACA;EACE,IAAAuB,0CAAmB,EACjB,MAAMhC,0BAA0B,CAACiB,KAAK,EACtCgB,MAAM,IAAI;IACR,MAAMC,MAAM,GAAG1B,4BAA4B,CAACS,KAAK;IACjD,IAAIgB,MAAM,IAAIC,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;MAC/B1B,mBAAmB,CAACyB,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IACjE;EACF,CAAC,EACD,EACF,CAAC;EACD;;EAEA,OAAO;IACLvB,KAAK,EAAET,aAAa;IACpBU,MAAM,EAAEP,cAAc;IACtB+B,eAAe,EAAE9B,uBAAuB;IACxC+B,iBAAiB,EAAE9B,yBAAyB;IAC5CP;EACF,CAAC;AACH,CAAC;AAACsC,OAAA,CAAAvC,WAAA,GAAAA,WAAA", "ignoreList": []}