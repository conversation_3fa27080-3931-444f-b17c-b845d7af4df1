{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_reactNativeGestureHandler", "_reactNativeReanimated", "_hooks", "_constants", "_styles", "_jsxRuntime", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "BottomSheetBackdropComponent", "animatedIndex", "opacity", "_providedOpacity", "appearsOnIndex", "_providedAppearsOnIndex", "disappearsOnIndex", "_providedDisappearsOnIndex", "enableTouchThrough", "_providedEnableTouchThrough", "pressBehavior", "DEFAULT_PRESS_BEHAVIOR", "onPress", "style", "children", "accessible", "_providedAccessible", "DEFAULT_ACCESSIBLE", "accessibilityRole", "_providedAccessibilityRole", "DEFAULT_ACCESSIBILITY_ROLE", "accessibilityLabel", "_providedAccessibilityLabel", "DEFAULT_ACCESSIBILITY_LABEL", "accessibilityHint", "_providedAccessibilityHint", "DEFAULT_ACCESSIBILITY_HINT", "snapToIndex", "close", "useBottomSheet", "isMounted", "useRef", "DEFAULT_OPACITY", "DEFAULT_APPEARS_ON_INDEX", "DEFAULT_DISAPPEARS_ON_INDEX", "DEFAULT_ENABLE_TOUCH_THROUGH", "pointerEvents", "setPointerEvents", "useState", "handleOnPress", "useCallback", "handleContainerTouchability", "shouldDisableTouchability", "current", "<PERSON><PERSON><PERSON><PERSON>", "useMemo", "gesture", "Gesture", "Tap", "onEnd", "runOnJS", "containerAnimatedStyle", "useAnimatedStyle", "interpolate", "value", "Extrapolation", "CLAMP", "containerStyle", "styles", "backdrop", "useAnimatedReaction", "previous", "useEffect", "AnimatedView", "jsx", "View", "undefined", "GestureDetector", "BottomSheetBackdrop", "exports", "memo", "displayName"], "sourceRoot": "../../../../src", "sources": ["components/bottomSheetBackdrop/BottomSheetBackdrop.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AASA,IAAAC,0BAAA,GAAAD,OAAA;AACA,IAAAE,sBAAA,GAAAH,uBAAA,CAAAC,OAAA;AAOA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,UAAA,GAAAJ,OAAA;AAWA,IAAAK,OAAA,GAAAL,OAAA;AAAkC,IAAAM,WAAA,GAAAN,OAAA;AAAA,SAAAO,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAT,wBAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAGlC,MAAMW,4BAA4B,GAAGA,CAAC;EACpCC,aAAa;EACbC,OAAO,EAAEC,gBAAgB;EACzBC,cAAc,EAAEC,uBAAuB;EACvCC,iBAAiB,EAAEC,0BAA0B;EAC7CC,kBAAkB,EAAEC,2BAA2B;EAC/CC,aAAa,GAAGC,iCAAsB;EACtCC,OAAO;EACPC,KAAK;EACLC,QAAQ;EACRC,UAAU,EAAEC,mBAAmB,GAAGC,6BAAkB;EACpDC,iBAAiB,EAAEC,0BAA0B,GAAGC,qCAA0B;EAC1EC,kBAAkB,EAAEC,2BAA2B,GAAGC,sCAA2B;EAC7EC,iBAAiB,EAAEC,0BAA0B,GAAGC;AACjB,CAAC,KAAK;EACrC;EACA,MAAM;IAAEC,WAAW;IAAEC;EAAM,CAAC,GAAG,IAAAC,qBAAc,EAAC,CAAC;EAC/C,MAAMC,SAAS,GAAG,IAAAC,aAAM,EAAC,KAAK,CAAC;EAC/B;;EAEA;EACA,MAAM7B,OAAO,GAAGC,gBAAgB,IAAI6B,0BAAe;EACnD,MAAM5B,cAAc,GAAGC,uBAAuB,IAAI4B,mCAAwB;EAC1E,MAAM3B,iBAAiB,GACrBC,0BAA0B,IAAI2B,sCAA2B;EAC3D,MAAM1B,kBAAkB,GACtBC,2BAA2B,IAAI0B,uCAA4B;EAC7D;;EAEA;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG,IAAAC,eAAQ,EAEhD9B,kBAAkB,GAAG,MAAM,GAAG,MAAM,CAAC;EACvC;;EAEA;EACA,MAAM+B,aAAa,GAAG,IAAAC,kBAAW,EAAC,MAAM;IACtC5B,OAAO,GAAG,CAAC;IAEX,IAAIF,aAAa,KAAK,OAAO,EAAE;MAC7BkB,KAAK,CAAC,CAAC;IACT,CAAC,MAAM,IAAIlB,aAAa,KAAK,UAAU,EAAE;MACvCiB,WAAW,CAACrB,iBAA2B,CAAC;IAC1C,CAAC,MAAM,IAAI,OAAOI,aAAa,KAAK,QAAQ,EAAE;MAC5CiB,WAAW,CAACjB,aAAa,CAAC;IAC5B;EACF,CAAC,EAAE,CAACiB,WAAW,EAAEC,KAAK,EAAEtB,iBAAiB,EAAEI,aAAa,EAAEE,OAAO,CAAC,CAAC;EACnE,MAAM6B,2BAA2B,GAAG,IAAAD,kBAAW,EAC5CE,yBAAkC,IAAK;IACtCZ,SAAS,CAACa,OAAO,IACfN,gBAAgB,CAACK,yBAAyB,GAAG,MAAM,GAAG,MAAM,CAAC;EACjE,CAAC,EACD,EACF,CAAC;EACD;;EAEA;EACA,MAAME,UAAU,GAAG,IAAAC,cAAO,EAAC,MAAM;IAC/B,MAAMC,OAAO,GAAGC,kCAAO,CAACC,GAAG,CAAC,CAAC,CAACC,KAAK,CAAC,MAAM;MACxC,IAAAC,8BAAO,EAACX,aAAa,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC;IACF,OAAOO,OAAO;EAChB,CAAC,EAAE,CAACP,aAAa,CAAC,CAAC;EACnB;;EAEA;EACA,MAAMY,sBAAsB,GAAG,IAAAC,uCAAgB,EAC7C,OAAO;IACLlD,OAAO,EAAE,IAAAmD,kCAAW,EAClBpD,aAAa,CAACqD,KAAK,EACnB,CAAC,CAAC,CAAC,EAAEhD,iBAAiB,EAAEF,cAAc,CAAC,EACvC,CAAC,CAAC,EAAE,CAAC,EAAEF,OAAO,CAAC,EACfqD,oCAAa,CAACC,KAChB;EACF,CAAC,CAAC,EACF,CAACvD,aAAa,EAAEG,cAAc,EAAEE,iBAAiB,EAAEJ,OAAO,CAC5D,CAAC;EACD,MAAMuD,cAAc,GAAG,IAAAZ,cAAO,EAC5B,MAAM,CAACa,cAAM,CAACC,QAAQ,EAAE9C,KAAK,EAAEsC,sBAAsB,CAAC,EACtD,CAACtC,KAAK,EAAEsC,sBAAsB,CAChC,CAAC;EACD;;EAEA;EACA,IAAAS,0CAAmB,EACjB,MAAM3D,aAAa,CAACqD,KAAK,IAAIhD,iBAAiB,EAC9C,CAACoC,yBAAyB,EAAEmB,QAAQ,KAAK;IACvC,IAAInB,yBAAyB,KAAKmB,QAAQ,EAAE;MAC1C;IACF;IACA,IAAAX,8BAAO,EAACT,2BAA2B,CAAC,CAACC,yBAAyB,CAAC;EACjE,CAAC,EACD,CAACpC,iBAAiB,CACpB,CAAC;;EAED;EACA;EACA,IAAAwD,gBAAS,EAAC,MAAM;IACdhC,SAAS,CAACa,OAAO,GAAG,IAAI;IACxB,OAAO,MAAM;MACXb,SAAS,CAACa,OAAO,GAAG,KAAK;IAC3B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN;;EAEA,MAAMoB,YAAY,gBAChB,IAAApF,WAAA,CAAAqF,GAAA,EAACzF,sBAAA,CAAAW,OAAQ,CAAC+E,IAAI;IACZpD,KAAK,EAAE4C,cAAe;IACtBrB,aAAa,EAAEA,aAAc;IAC7BrB,UAAU,EAAEC,mBAAmB,IAAIkD,SAAU;IAC7ChD,iBAAiB,EAAEC,0BAA0B,IAAI+C,SAAU;IAC3D7C,kBAAkB,EAAEC,2BAA2B,IAAI4C,SAAU;IAC7D1C,iBAAiB,EACfC,0BAA0B,GACtBA,0BAA0B,GAC1B,UACE,OAAOf,aAAa,KAAK,QAAQ,GAAGA,aAAa,GAAG,MAAM,mBAEjE;IAAAI,QAAA,EAEAA;EAAQ,CACI,CAChB;EAED,OAAOJ,aAAa,KAAK,MAAM,gBAC7B,IAAA/B,WAAA,CAAAqF,GAAA,EAAC1F,0BAAA,CAAA6F,eAAe;IAACrB,OAAO,EAAEF,UAAW;IAAA9B,QAAA,EAAEiD;EAAY,CAAkB,CAAC,GAEtEA,YACD;AACH,CAAC;AAEM,MAAMK,mBAAmB,GAAAC,OAAA,CAAAD,mBAAA,gBAAG,IAAAE,WAAI,EAACtE,4BAA4B,CAAC;AACrEoE,mBAAmB,CAACG,WAAW,GAAG,qBAAqB", "ignoreList": []}