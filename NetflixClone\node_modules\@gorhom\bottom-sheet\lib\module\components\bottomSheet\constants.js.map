{"version": 3, "names": ["KEYBOARD_BEHAVIOR", "KEYBOARD_BLUR_BEHAVIOR", "KEYBOARD_INPUT_MODE", "SCREEN_HEIGHT", "DEFAULT_HANDLE_HEIGHT", "DEFAULT_OVER_DRAG_RESISTANCE_FACTOR", "DEFAULT_ENABLE_CONTENT_PANNING_GESTURE", "DEFAULT_ENABLE_HANDLE_PANNING_GESTURE", "DEFAULT_ENABLE_OVER_DRAG", "DEFAULT_ENABLE_PAN_DOWN_TO_CLOSE", "DEFAULT_ANIMATE_ON_MOUNT", "DEFAULT_DYNAMIC_SIZING", "DEFAULT_KEYBOARD_BEHAVIOR", "interactive", "DEFAULT_KEYBOARD_BLUR_BEHAVIOR", "none", "DEFAULT_KEYBOARD_INPUT_MODE", "adjustPan", "DEFAULT_ENABLE_BLUR_KEYBOARD_ON_GESTURE", "INITIAL_VALUE", "Number", "NEGATIVE_INFINITY", "INITIAL_SNAP_POINT", "INITIAL_CONTAINER_HEIGHT", "INITIAL_CONTAINER_OFFSET", "top", "bottom", "left", "right", "INITIAL_HANDLE_HEIGHT", "INITIAL_POSITION", "DEFAULT_ACCESSIBLE", "DEFAULT_ACCESSIBILITY_LABEL", "DEFAULT_ACCESSIBILITY_ROLE"], "sourceRoot": "../../../../src", "sources": ["components/bottomSheet/constants.ts"], "mappings": ";;AAAA,SACEA,iBAAiB,EACjBC,sBAAsB,EACtBC,mBAAmB,EACnBC,aAAa,QACR,iBAAiB;;AAExB;AACA,MAAMC,qBAAqB,GAAG,EAAE;AAChC,MAAMC,mCAAmC,GAAG,GAAG;AAC/C,MAAMC,sCAAsC,GAAG,IAAI;AACnD,MAAMC,qCAAqC,GAAG,IAAI;AAClD,MAAMC,wBAAwB,GAAG,IAAI;AACrC,MAAMC,gCAAgC,GAAG,KAAK;AAC9C,MAAMC,wBAAwB,GAAG,IAAI;AACrC,MAAMC,sBAAsB,GAAG,IAAI;;AAEnC;AACA,MAAMC,yBAAyB,GAAGZ,iBAAiB,CAACa,WAAW;AAC/D,MAAMC,8BAA8B,GAAGb,sBAAsB,CAACc,IAAI;AAClE,MAAMC,2BAA2B,GAAGd,mBAAmB,CAACe,SAAS;AACjE,MAAMC,uCAAuC,GAAG,KAAK;;AAErD;AACA,MAAMC,aAAa,GAAGC,MAAM,CAACC,iBAAiB;AAC9C,MAAMC,kBAAkB,GAAG,CAAC,GAAG;AAC/B,MAAMC,wBAAwB,GAAG,CAAC,GAAG;AACrC,MAAMC,wBAAwB,GAAG;EAC/BC,GAAG,EAAE,CAAC;EACNC,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE;AACT,CAAC;AACD,MAAMC,qBAAqB,GAAG,CAAC,GAAG;AAClC,MAAMC,gBAAgB,GAAG3B,aAAa;;AAEtC;AACA,MAAM4B,kBAAkB,GAAG,IAAI;AAC/B,MAAMC,2BAA2B,GAAG,cAAc;AAClD,MAAMC,0BAA0B,GAAG,YAAY;AAE/C,SACE7B,qBAAqB,EACrBC,mCAAmC,EACnCC,sCAAsC,EACtCC,qCAAqC,EACrCC,wBAAwB,EACxBC,gCAAgC,EAChCE,sBAAsB,EACtBD,wBAAwB;AACxB;AACAE,yBAAyB,EACzBE,8BAA8B,EAC9BE,2BAA2B,EAC3BE,uCAAuC;AACvC;AACAY,gBAAgB,EAChBP,wBAAwB,EACxBC,wBAAwB,EACxBK,qBAAqB,EACrBP,kBAAkB,EAClBH,aAAa;AACb;AACAY,kBAAkB,EAClBC,2BAA2B,EAC3BC,0BAA0B", "ignoreList": []}