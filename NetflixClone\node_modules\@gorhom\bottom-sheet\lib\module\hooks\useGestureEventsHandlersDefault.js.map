{"version": 3, "names": ["Keyboard", "Platform", "runOnJS", "useSharedValue", "useWorkletCallback", "ANIMATION_SOURCE", "GESTURE_SOURCE", "KEYBOARD_STATE", "SCROLLABLE_TYPE", "WINDOW_HEIGHT", "clamp", "snapPoint", "useBottomSheetInternal", "INITIAL_CONTEXT", "initialPosition", "initialKeyboardState", "UNDETERMINED", "isScrollablePositionLocked", "dismissKeyboard", "dismiss", "resetContext", "context", "Object", "keys", "map", "key", "undefined", "useGestureEventsHandlersDefault", "animatedPosition", "animatedSnapPoints", "animatedKeyboardState", "animatedKeyboardHeight", "animatedContainerHeight", "animatedScrollableType", "animatedHighestSnapPoint", "animatedClosedPosition", "animatedScrollableContentOffsetY", "enableOverDrag", "enablePanDownToClose", "overDragResistanceFactor", "isInTemporaryPosition", "isScrollableRefreshable", "enableBlurKeyboardOnGesture", "animateToPosition", "stopAnimation", "handleOnStart", "__", "_", "value", "SHOWN", "HIDDEN", "handleOnChange", "source", "translationY", "highestSnapPoint", "lowestSnapPoint", "CONTENT", "negativeScrollableContentOffset", "draggedPosition", "accumulatedDraggedPosition", "clampedPosition", "HANDLE", "VIEW", "resistedPosition", "Math", "sqrt", "handleOnEnd", "absoluteY", "velocityY", "isSheetAtHighestSnapPoint", "GESTURE", "isScrollable", "OS", "snapPoints", "slice", "unshift", "destinationPoint", "wasGestureHandledByScrollView", "handleOnFinalize"], "sourceRoot": "../../../src", "sources": ["hooks/useGestureEventsHandlersDefault.tsx"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,QAAQ,QAAQ,cAAc;AACjD,SACEC,OAAO,EACPC,cAAc,EACdC,kBAAkB,QACb,yBAAyB;AAChC,SACEC,gBAAgB,EAChBC,cAAc,EACdC,cAAc,EACdC,eAAe,EACfC,aAAa,QACR,cAAc;AAKrB,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,sBAAsB,QAAQ,0BAA0B;AAQjE,MAAMC,eAAwC,GAAG;EAC/CC,eAAe,EAAE,CAAC;EAClBC,oBAAoB,EAAER,cAAc,CAACS,YAAY;EACjDC,0BAA0B,EAAE;AAC9B,CAAC;AAED,MAAMC,eAAe,GAAGlB,QAAQ,CAACmB,OAAO;;AAExC;AACA,MAAMC,YAAY,GAAIC,OAAY,IAAK;EACrC,SAAS;;EACTC,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACG,GAAG,CAACC,GAAG,IAAI;IAC9BJ,OAAO,CAACI,GAAG,CAAC,GAAGC,SAAS;EAC1B,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,+BAA8D,GACzEA,CAAA,KAAM;EACJ;EACA,MAAM;IACJC,gBAAgB;IAChBC,kBAAkB;IAClBC,qBAAqB;IACrBC,sBAAsB;IACtBC,uBAAuB;IACvBC,sBAAsB;IACtBC,wBAAwB;IACxBC,sBAAsB;IACtBC,gCAAgC;IAChCC,cAAc;IACdC,oBAAoB;IACpBC,wBAAwB;IACxBC,qBAAqB;IACrBC,uBAAuB;IACvBC,2BAA2B;IAC3BC,iBAAiB;IACjBC;EACF,CAAC,GAAGhC,sBAAsB,CAAC,CAAC;EAE5B,MAAMS,OAAO,GAAGlB,cAAc,CAA0B;IACtD,GAAGU;EACL,CAAC,CAAC;EACF;;EAEA;EACA,MAAMgC,aAA8C,GAAGzC,kBAAkB,CACvE,SAASyC,aAAaA,CAACC,EAAE,EAAEC,CAAC,EAAE;IAC5B;IACAH,aAAa,CAAC,CAAC;IAEf,IAAI7B,oBAAoB,GAAGe,qBAAqB,CAACkB,KAAK;IACtD;IACA,IACEN,2BAA2B,IAC3B3B,oBAAoB,KAAKR,cAAc,CAAC0C,KAAK,EAC7C;MACAlC,oBAAoB,GAAGR,cAAc,CAAC2C,MAAM;MAC5ChD,OAAO,CAACgB,eAAe,CAAC,CAAC,CAAC;IAC5B;;IAEA;IACAG,OAAO,CAAC2B,KAAK,GAAG;MACd,GAAG3B,OAAO,CAAC2B,KAAK;MAChBlC,eAAe,EAAEc,gBAAgB,CAACoB,KAAK;MACvCjC,oBAAoB,EAAEe,qBAAqB,CAACkB;IAC9C,CAAC;;IAED;AACR;AACA;AACA;IACQ,IAAIZ,gCAAgC,CAACY,KAAK,GAAG,CAAC,EAAE;MAC9C3B,OAAO,CAAC2B,KAAK,GAAG;QACd,GAAG3B,OAAO,CAAC2B,KAAK;QAChB/B,0BAA0B,EAAE;MAC9B,CAAC;IACH;EACF,CAAC,EACD,CACE2B,aAAa,EACbF,2BAA2B,EAC3Bd,gBAAgB,EAChBE,qBAAqB,EACrBM,gCAAgC,CAEpC,CAAC;EACD,MAAMe,cAA+C,GAAG/C,kBAAkB,CACxE,SAAS+C,cAAcA,CAACC,MAAM,EAAE;IAAEC;EAAa,CAAC,EAAE;IAChD,IAAIC,gBAAgB,GAAGpB,wBAAwB,CAACc,KAAK;;IAErD;AACR;AACA;AACA;IACQ,IACER,qBAAqB,CAACQ,KAAK,IAC3B3B,OAAO,CAAC2B,KAAK,CAACjC,oBAAoB,KAAKR,cAAc,CAAC0C,KAAK,EAC3D;MACAK,gBAAgB,GAAGjC,OAAO,CAAC2B,KAAK,CAAClC,eAAe;IAClD;;IAEA;AACR;AACA;AACA;IACQ,IACE0B,qBAAqB,CAACQ,KAAK,IAC3B3B,OAAO,CAAC2B,KAAK,CAAClC,eAAe,GAAGwC,gBAAgB,EAChD;MACAA,gBAAgB,GAAGjC,OAAO,CAAC2B,KAAK,CAAClC,eAAe;IAClD;IAEA,MAAMyC,eAAe,GAAGjB,oBAAoB,GACxCN,uBAAuB,CAACgB,KAAK,GAC7BnB,kBAAkB,CAACmB,KAAK,CAAC,CAAC,CAAC;;IAE/B;AACR;AACA;AACA;IACQ,IACEI,MAAM,KAAK9C,cAAc,CAACkD,OAAO,IACjCf,uBAAuB,CAACO,KAAK,IAC7BpB,gBAAgB,CAACoB,KAAK,KAAKM,gBAAgB,EAC3C;MACA;IACF;;IAEA;AACR;AACA;AACA;AACA;AACA;IACQ,MAAMG,+BAA+B,GAClCpC,OAAO,CAAC2B,KAAK,CAAClC,eAAe,KAAKwC,gBAAgB,IACjDF,MAAM,KAAK9C,cAAc,CAACkD,OAAO,IACnC,CAACnC,OAAO,CAAC2B,KAAK,CAAC/B,0BAA0B,GACrCmB,gCAAgC,CAACY,KAAK,GAAG,CAAC,CAAC,GAC3C,CAAC;;IAEP;AACR;AACA;IACQ,MAAMU,eAAe,GAAGrC,OAAO,CAAC2B,KAAK,CAAClC,eAAe,GAAGuC,YAAY;;IAEpE;AACR;AACA;AACA;AACA;IACQ,MAAMM,0BAA0B,GAC9BD,eAAe,GAAGD,+BAA+B;;IAEnD;AACR;AACA;AACA;IACQ,MAAMG,eAAe,GAAGlD,KAAK,CAC3BiD,0BAA0B,EAC1BL,gBAAgB,EAChBC,eACF,CAAC;;IAED;AACR;AACA;AACA;IACQ,IACElC,OAAO,CAAC2B,KAAK,CAAC/B,0BAA0B,IACxCmC,MAAM,KAAK9C,cAAc,CAACkD,OAAO,IACjC5B,gBAAgB,CAACoB,KAAK,KAAKM,gBAAgB,EAC3C;MACAjC,OAAO,CAAC2B,KAAK,GAAG;QACd,GAAG3B,OAAO,CAAC2B,KAAK;QAChB/B,0BAA0B,EAAE;MAC9B,CAAC;IACH;;IAEA;AACR;AACA;IACQ,IAAIoB,cAAc,EAAE;MAClB,IACE,CAACe,MAAM,KAAK9C,cAAc,CAACuD,MAAM,IAC/B5B,sBAAsB,CAACe,KAAK,KAAKxC,eAAe,CAACsD,IAAI,KACvDJ,eAAe,GAAGJ,gBAAgB,EAClC;QACA,MAAMS,gBAAgB,GACpBT,gBAAgB,GAChBU,IAAI,CAACC,IAAI,CAAC,CAAC,IAAIX,gBAAgB,GAAGI,eAAe,CAAC,CAAC,GACjDnB,wBAAwB;QAC5BX,gBAAgB,CAACoB,KAAK,GAAGe,gBAAgB;QACzC;MACF;MAEA,IACEX,MAAM,KAAK9C,cAAc,CAACuD,MAAM,IAChCH,eAAe,GAAGH,eAAe,EACjC;QACA,MAAMQ,gBAAgB,GACpBR,eAAe,GACfS,IAAI,CAACC,IAAI,CAAC,CAAC,IAAIP,eAAe,GAAGH,eAAe,CAAC,CAAC,GAChDhB,wBAAwB;QAC5BX,gBAAgB,CAACoB,KAAK,GAAGe,gBAAgB;QACzC;MACF;MAEA,IACEX,MAAM,KAAK9C,cAAc,CAACkD,OAAO,IACjCE,eAAe,GAAGD,+BAA+B,GAAGF,eAAe,EACnE;QACA,MAAMQ,gBAAgB,GACpBR,eAAe,GACfS,IAAI,CAACC,IAAI,CACP,CAAC,IACEP,eAAe,GACdD,+BAA+B,GAC/BF,eAAe,CACrB,CAAC,GACChB,wBAAwB;QAC5BX,gBAAgB,CAACoB,KAAK,GAAGe,gBAAgB;QACzC;MACF;IACF;IAEAnC,gBAAgB,CAACoB,KAAK,GAAGY,eAAe;EAC1C,CAAC,EACD,CACEvB,cAAc,EACdC,oBAAoB,EACpBC,wBAAwB,EACxBC,qBAAqB,EACrBC,uBAAuB,EACvBP,wBAAwB,EACxBF,uBAAuB,EACvBH,kBAAkB,EAClBD,gBAAgB,EAChBK,sBAAsB,EACtBG,gCAAgC,CAEpC,CAAC;EACD,MAAM8B,WAA4C,GAAG9D,kBAAkB,CACrE,SAAS8D,WAAWA,CAACd,MAAM,EAAE;IAAEC,YAAY;IAAEc,SAAS;IAAEC;EAAU,CAAC,EAAE;IACnE,MAAMd,gBAAgB,GAAGpB,wBAAwB,CAACc,KAAK;IACvD,MAAMqB,yBAAyB,GAC7BzC,gBAAgB,CAACoB,KAAK,KAAKM,gBAAgB;;IAE7C;AACR;AACA;AACA;IACQ,IACEF,MAAM,KAAK9C,cAAc,CAACkD,OAAO,IACjCf,uBAAuB,CAACO,KAAK,IAC7BqB,yBAAyB,EACzB;MACA;IACF;;IAEA;AACR;AACA;AACA;IACQ,IACE7B,qBAAqB,CAACQ,KAAK,IAC3B3B,OAAO,CAAC2B,KAAK,CAAClC,eAAe,IAAIc,gBAAgB,CAACoB,KAAK,EACvD;MACA,IAAI3B,OAAO,CAAC2B,KAAK,CAAClC,eAAe,GAAGc,gBAAgB,CAACoB,KAAK,EAAE;QAC1DL,iBAAiB,CACftB,OAAO,CAAC2B,KAAK,CAAClC,eAAe,EAC7BT,gBAAgB,CAACiE,OAAO,EACxBF,SAAS,GAAG,CACd,CAAC;MACH;MACA;IACF;;IAEA;AACR;AACA;AACA;IACQ,MAAMG,YAAY,GAChBtC,sBAAsB,CAACe,KAAK,KAAKxC,eAAe,CAACQ,YAAY,IAC7DiB,sBAAsB,CAACe,KAAK,KAAKxC,eAAe,CAACsD,IAAI;;IAEvD;AACR;AACA;AACA;IACQ,IACEzC,OAAO,CAAC2B,KAAK,CAACjC,oBAAoB,KAAKR,cAAc,CAAC0C,KAAK,IAC3DrB,gBAAgB,CAACoB,KAAK,GAAG3B,OAAO,CAAC2B,KAAK,CAAClC,eAAe,EACtD;MACA;AACV;AACA;AACA;AACA;AACA;AACA;MACU,IACE,EACEb,QAAQ,CAACuE,EAAE,KAAK,KAAK,IACrBD,YAAY,IACZJ,SAAS,GAAG1D,aAAa,GAAGsB,sBAAsB,CAACiB,KAAK,CACzD,EACD;QACA9C,OAAO,CAACgB,eAAe,CAAC,CAAC,CAAC;MAC5B;IACF;;IAEA;AACR;AACA;IACQ,IAAIsB,qBAAqB,CAACQ,KAAK,EAAE;MAC/BR,qBAAqB,CAACQ,KAAK,GAAG,KAAK;IACrC;;IAEA;AACR;AACA;AACA;IACQ,MAAMyB,UAAU,GAAG5C,kBAAkB,CAACmB,KAAK,CAAC0B,KAAK,CAAC,CAAC;IACnD,IAAIpC,oBAAoB,EAAE;MACxBmC,UAAU,CAACE,OAAO,CAACxC,sBAAsB,CAACa,KAAK,CAAC;IAClD;;IAEA;AACR;AACA;IACQ,MAAM4B,gBAAgB,GAAGjE,SAAS,CAChC0C,YAAY,GAAGhC,OAAO,CAAC2B,KAAK,CAAClC,eAAe,EAC5CsD,SAAS,EACTK,UACF,CAAC;;IAED;AACR;AACA;AACA;IACQ,IAAIG,gBAAgB,KAAKhD,gBAAgB,CAACoB,KAAK,EAAE;MAC/C;IACF;IAEA,MAAM6B,6BAA6B,GACjCzB,MAAM,KAAK9C,cAAc,CAACkD,OAAO,IACjCpB,gCAAgC,CAACY,KAAK,GAAG,CAAC;IAC5C;AACR;AACA;IACQ,IAAI6B,6BAA6B,IAAIR,yBAAyB,EAAE;MAC9D;IACF;IAEA1B,iBAAiB,CACfiC,gBAAgB,EAChBvE,gBAAgB,CAACiE,OAAO,EACxBF,SAAS,GAAG,CACd,CAAC;EACH,CAAC,EACD,CACE9B,oBAAoB,EACpBE,qBAAqB,EACrBC,uBAAuB,EACvBN,sBAAsB,EACtBD,wBAAwB,EACxBH,sBAAsB,EACtBH,gBAAgB,EAChBK,sBAAsB,EACtBJ,kBAAkB,EAClBO,gCAAgC,EAChCO,iBAAiB,CAErB,CAAC;EAED,MAAMmC,gBAAiD,GACrD1E,kBAAkB,CAChB,SAAS0E,gBAAgBA,CAAA,EAAG;IAC1B1D,YAAY,CAACC,OAAO,CAAC;EACvB,CAAC,EACD,CAACA,OAAO,CACV,CAAC;EACH;;EAEA,OAAO;IACLwB,aAAa;IACbM,cAAc;IACde,WAAW;IACXY;EACF,CAAC;AACH,CAAC", "ignoreList": []}