{"version": 3, "sources": ["PortalProvider.tsx"], "names": ["PortalProviderComponent", "rootHostName", "shouldAddRootHost", "children", "state", "dispatch", "reducer", "INITIAL_STATE", "PortalProvider", "displayName"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AAIA;;AACA;;;;;;AAGA,MAAMA,uBAAuB,GAAG,CAAC;AAC/BC,EAAAA,YAAY,GAAG,MADgB;AAE/BC,EAAAA,iBAAiB,GAAG,IAFW;AAG/BC,EAAAA;AAH+B,CAAD,KAIL;AACzB,QAAM,CAACC,KAAD,EAAQC,QAAR,IAAoB,uBAAWC,gBAAX,EAAoBC,wBAApB,CAA1B;AACA,sBACE,6BAAC,6BAAD,CAAuB,QAAvB;AAAgC,IAAA,KAAK,EAAEF;AAAvC,kBACE,6BAAC,0BAAD,CAAoB,QAApB;AAA6B,IAAA,KAAK,EAAED;AAApC,KACGD,QADH,EAEGD,iBAAiB,iBAAI,6BAAC,sBAAD;AAAY,IAAA,IAAI,EAAED;AAAlB,IAFxB,CADF,CADF;AAQD,CAdD;;AAgBO,MAAMO,cAAc,gBAAG,iBAAKR,uBAAL,CAAvB;;AACPQ,cAAc,CAACC,WAAf,GAA6B,gBAA7B", "sourcesContent": ["import React, { memo, useReducer } from 'react';\nimport { PortalHost } from '../portalHost/PortalHost';\nimport {\n  PortalDispatchContext,\n  PortalStateContext,\n} from '../../contexts/portal';\nimport { INITIAL_STATE } from '../../state/constants';\nimport { reducer } from '../../state/reducer';\nimport type { PortalProviderProps } from './types';\n\nconst PortalProviderComponent = ({\n  rootHostName = 'root',\n  shouldAddRootHost = true,\n  children,\n}: PortalProviderProps) => {\n  const [state, dispatch] = useReducer(reducer, INITIAL_STATE);\n  return (\n    <PortalDispatchContext.Provider value={dispatch}>\n      <PortalStateContext.Provider value={state}>\n        {children}\n        {shouldAddRootHost && <PortalHost name={rootHostName} />}\n      </PortalStateContext.Provider>\n    </PortalDispatchContext.Provider>\n  );\n};\n\nexport const PortalProvider = memo(PortalProviderComponent);\nPortalProvider.displayName = 'PortalProvider';\n"]}