{"version": 3, "names": ["_react", "require", "_internal", "useBottomSheetInternal", "unsafe", "context", "useContext", "BottomSheetInternalContext"], "sourceRoot": "../../../src", "sources": ["hooks/useBottomSheetInternal.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AAaO,SAASE,sBAAsBA,CACpCC,MAAgB,EACuB;EACvC,MAAMC,OAAO,GAAG,IAAAC,iBAAU,EAACC,oCAA0B,CAAC;EAEtD,IAAIH,MAAM,KAAK,IAAI,IAAIC,OAAO,KAAK,IAAI,EAAE;IACvC,MAAM,iEAAiE;EACzE;EAEA,OAAOA,OAAO;AAChB", "ignoreList": []}