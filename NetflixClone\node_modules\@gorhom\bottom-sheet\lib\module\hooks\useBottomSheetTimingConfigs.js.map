{"version": 3, "names": ["useMemo", "ANIMATION_DURATION", "ANIMATION_EASING", "useBottomSheetTimingConfigs", "configs", "_configs", "easing", "duration", "reduceMotion"], "sourceRoot": "../../../src", "sources": ["hooks/useBottomSheetTimingConfigs.ts"], "mappings": ";;AAAA,SAASA,OAAO,QAAQ,OAAO;AAM/B,SAASC,kBAAkB,EAAEC,gBAAgB,QAAQ,cAAc;;AAEnE;AACA;AACA;AACA;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,2BAA2B,GAAIC,OAAqB,IAAK;EACpE,OAAOJ,OAAO,CAAC,MAAM;IACnB,MAAMK,QAAsB,GAAG;MAC7BC,MAAM,EAAEF,OAAO,CAACE,MAAM,IAAIJ,gBAAgB;MAC1CK,QAAQ,EAAEH,OAAO,CAACG,QAAQ,IAAIN,kBAAkB;MAChDO,YAAY,EAAEJ,OAAO,CAACI;IACxB,CAAC;IAED,OAAOH,QAAQ;EACjB,CAAC,EAAE,CAACD,OAAO,CAACG,QAAQ,EAAEH,OAAO,CAACE,MAAM,EAAEF,OAAO,CAACI,YAAY,CAAC,CAAC;AAC9D,CAAC", "ignoreList": []}