{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_reactNativeReanimated", "_constants", "_hooks", "_constants2", "_jsxRuntime", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "BottomSheetFooterContainerComponent", "footerComponent", "FooterComponent", "animatedContainerHeight", "animatedHandleHeight", "animatedFooterHeight", "animatedPosition", "animatedKeyboardState", "animatedKeyboardHeightInContainer", "useBottomSheetInternal", "animatedFooterPosition", "useDerivedValue", "handleHeight", "INITIAL_HANDLE_HEIGHT", "keyboardHeight", "containerHeight", "position", "keyboardState", "footerHeight", "footerTranslateY", "Math", "max", "KEYBOARD_STATE", "SHOWN", "jsx", "BottomSheetFooterContainer", "exports", "memo", "displayName"], "sourceRoot": "../../../../src", "sources": ["components/bottomSheetFooter/BottomSheetFooterContainer.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,sBAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,WAAA,GAAAJ,OAAA;AAAiE,IAAAK,WAAA,GAAAL,OAAA;AAAA,SAAAM,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAR,wBAAAQ,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAGjE,MAAMW,mCAAmC,GAAGA,CAAC;EAC3CC,eAAe,EAAEC;AACc,CAAC,KAAK;EACrC;EACA,MAAM;IACJC,uBAAuB;IACvBC,oBAAoB;IACpBC,oBAAoB;IACpBC,gBAAgB;IAChBC,qBAAqB;IACrBC;EACF,CAAC,GAAG,IAAAC,6BAAsB,EAAC,CAAC;EAC5B;;EAEA;EACA,MAAMC,sBAAsB,GAAG,IAAAC,sCAAe,EAAC,MAAM;IACnD,MAAMC,YAAY,GAAGR,oBAAoB,CAAChB,GAAG,CAAC,CAAC;IAC/C,IAAIwB,YAAY,KAAKC,iCAAqB,EAAE;MAC1C,OAAO,CAAC;IACV;IAEA,MAAMC,cAAc,GAAGN,iCAAiC,CAACpB,GAAG,CAAC,CAAC;IAC9D,MAAM2B,eAAe,GAAGZ,uBAAuB,CAACf,GAAG,CAAC,CAAC;IACrD,MAAM4B,QAAQ,GAAGV,gBAAgB,CAAClB,GAAG,CAAC,CAAC;IACvC,MAAM6B,aAAa,GAAGV,qBAAqB,CAACnB,GAAG,CAAC,CAAC;IACjD,MAAM8B,YAAY,GAAGb,oBAAoB,CAACjB,GAAG,CAAC,CAAC;IAE/C,IAAI+B,gBAAgB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEN,eAAe,GAAGC,QAAQ,CAAC;IAC9D,IAAIC,aAAa,KAAKK,yBAAc,CAACC,KAAK,EAAE;MAC1CJ,gBAAgB,GAAGA,gBAAgB,GAAGL,cAAc;IACtD;IAEAK,gBAAgB,GAAGA,gBAAgB,GAAGD,YAAY,GAAGN,YAAY;IACjE,OAAOO,gBAAgB;EACzB,CAAC,EAAE,CACDX,iCAAiC,EACjCL,uBAAuB,EACvBG,gBAAgB,EAChBC,qBAAqB,EACrBF,oBAAoB,EACpBD,oBAAoB,CACrB,CAAC;EACF;;EAEA,oBAAO,IAAAzB,WAAA,CAAA6C,GAAA,EAACtB,eAAe;IAACQ,sBAAsB,EAAEA;EAAuB,CAAE,CAAC;AAC5E,CAAC;AAEM,MAAMe,0BAA0B,GAAAC,OAAA,CAAAD,0BAAA,gBAAG,IAAAE,WAAI,EAC5C3B,mCACF,CAAC;AACDyB,0BAA0B,CAACG,WAAW,GAAG,4BAA4B", "ignoreList": []}