{"version": 3, "names": ["_react", "require", "_isFabricInstalled", "useBoundingClientRect", "ref", "handler", "isFabricInstalled", "useLayoutEffect", "current", "unstable_getBoundingClientRect", "layout", "getBoundingClientRect"], "sourceRoot": "../../../src", "sources": ["hooks/useBoundingClientRect.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAEA,IAAAC,kBAAA,GAAAD,OAAA;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASE,qBAAqBA,CACnCC,GAA2B,EAC3BC,OAA6C,EAC7C;EACA,IAAI,CAAC,IAAAC,oCAAiB,EAAC,CAAC,EAAE;IACxB;EACF;;EAEA;EACA,IAAAC,sBAAe,EAAC,MAAM;IACpB,IAAI,CAACH,GAAG,IAAI,CAACA,GAAG,CAACI,OAAO,EAAE;MACxB;IACF;;IAEA;IACA,IAAIJ,GAAG,CAACI,OAAO,CAACC,8BAA8B,KAAK,IAAI,EAAE;MACvD;MACA,MAAMC,MAAM,GAAGN,GAAG,CAACI,OAAO,CAACC,8BAA8B,CAAC,CAAC;MAC3DJ,OAAO,CAACK,MAAM,CAAC;MACf;IACF;;IAEA;IACA,IAAIN,GAAG,CAACI,OAAO,CAACG,qBAAqB,KAAK,IAAI,EAAE;MAC9C;MACA,MAAMD,MAAM,GAAGN,GAAG,CAACI,OAAO,CAACG,qBAAqB,CAAC,CAAC;MAClDN,OAAO,CAACK,MAAM,CAAC;IACjB;EACF,CAAC,CAAC;AACJ", "ignoreList": []}