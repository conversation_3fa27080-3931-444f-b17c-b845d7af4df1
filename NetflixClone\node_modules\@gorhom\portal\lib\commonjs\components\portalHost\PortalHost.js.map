{"version": 3, "sources": ["PortalHost.tsx"], "names": ["PortalHostComponent", "name", "state", "registerHost", "deregisterHost", "map", "item", "node", "PortalHost", "displayName"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;;;;;AAGA,MAAMA,mBAAmB,GAAG,CAAC;AAAEC,EAAAA;AAAF,CAAD,KAA+B;AACzD;AACA,QAAMC,KAAK,GAAG,oCAAeD,IAAf,CAAd;AACA,QAAM;AAAEE,IAAAA,YAAF;AAAgBC,IAAAA;AAAhB,MAAmC,0BAAUH,IAAV,CAAzC,CAHyD,CAIzD;AAEA;;AACA,wBAAU,MAAM;AACdE,IAAAA,YAAY;AACZ,WAAO,MAAM;AACXC,MAAAA,cAAc;AACf,KAFD,CAFc,CAKd;AACD,GAND,EAMG,EANH,EAPyD,CAczD;AAEA;;AACA,sBAAO,4DAAGF,KAAK,CAACG,GAAN,CAAUC,IAAI,IAAIA,IAAI,CAACC,IAAvB,CAAH,CAAP,CAjByD,CAkBzD;AACD,CAnBD;;AAqBO,MAAMC,UAAU,gBAAG,iBAAKR,mBAAL,CAAnB;;AACPQ,UAAU,CAACC,WAAX,GAAyB,YAAzB", "sourcesContent": ["import React, { memo, useEffect } from 'react';\nimport { usePortalState } from '../../hooks/usePortalState';\nimport { usePortal } from '../../hooks/usePortal';\nimport type { PortalHostProps } from './types';\n\nconst PortalHostComponent = ({ name }: PortalHostProps) => {\n  //#region hooks\n  const state = usePortalState(name);\n  const { registerHost, deregisterHost } = usePortal(name);\n  //#endregion\n\n  //#region effects\n  useEffect(() => {\n    registerHost();\n    return () => {\n      deregisterHost();\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  //#endregion\n\n  //#region render\n  return <>{state.map(item => item.node)}</>;\n  //#endregion\n};\n\nexport const PortalHost = memo(PortalHostComponent);\nPortalHost.displayName = 'PortalHost';\n"]}