{"version": 3, "names": ["invariant", "validateSnapPoint", "snapPoint", "includes", "Number", "split"], "sourceRoot": "../../../src", "sources": ["utilities/validateSnapPoint.ts"], "mappings": ";;AAAA,OAAOA,SAAS,MAAM,WAAW;AAEjC,OAAO,MAAMC,iBAAiB,GAAIC,SAA0B,IAAK;EAC/DF,SAAS,CACP,OAAOE,SAAS,KAAK,QAAQ,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAC9D,IAAIA,SAAS,mEACf,CAAC;EAEDF,SAAS,CACP,OAAOE,SAAS,KAAK,QAAQ,IAC1B,OAAOA,SAAS,KAAK,QAAQ,IAAIA,SAAS,CAACC,QAAQ,CAAC,GAAG,CAAE,EAC5D,IAAID,SAAS,qGACf,CAAC;EAEDF,SAAS,CACP,OAAOE,SAAS,KAAK,QAAQ,IAC1B,OAAOA,SAAS,KAAK,QAAQ,IAAIE,MAAM,CAACF,SAAS,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAE,EACpE,IAAIH,SAAS,iHACf,CAAC;AACH,CAAC", "ignoreList": []}