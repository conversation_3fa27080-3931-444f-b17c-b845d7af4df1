import React from 'react';
import type { BottomSheetProps } from '../bottomSheet/types';
type BottomSheetBodyProps = {
    style?: BottomSheetProps['style'];
    children?: React.ReactNode;
};
declare function BottomSheetBodyComponent({ style, children }: BottomSheetBodyProps): React.JSX.Element;
export declare const BottomSheetBody: React.MemoExoticComponent<typeof BottomSheetBodyComponent>;
export {};
//# sourceMappingURL=BottomSheetBody.d.ts.map