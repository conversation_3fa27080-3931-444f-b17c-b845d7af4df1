{"version": 3, "names": ["State", "scrollTo", "useWorkletCallback", "ANIMATION_STATE", "SCROLLABLE_STATE", "SHEET_STATE", "useBottomSheetInternal", "useScrollEventsHandlersDefault", "scrollableRef", "scrollableContentOffsetY", "animatedSheetState", "animatedScrollableState", "animatedAnimationState", "animatedHandleGestureState", "animatedScrollableContentOffsetY", "rootScrollableContentOffsetY", "handleOnScroll", "contentOffset", "y", "context", "value", "EXTENDED", "FILL_PARENT", "shouldLockInitialPosition", "ACTIVE", "initialContentOffsetY", "LOCKED", "lockPosition", "handleOnBeginDrag", "handleOnEndDrag", "RUNNING", "handleOnMomentumEnd"], "sourceRoot": "../../../src", "sources": ["hooks/useScrollEventsHandlersDefault.ts"], "mappings": ";;AAAA,SAASA,KAAK,QAAQ,8BAA8B;AACpD,SAASC,QAAQ,EAAEC,kBAAkB,QAAQ,yBAAyB;AACtE,SAASC,eAAe,EAAEC,gBAAgB,EAAEC,WAAW,QAAQ,cAAc;AAK7E,SAASC,sBAAsB,QAAQ,0BAA0B;AAOjE,OAAO,MAAMC,8BAA4D,GAAGA,CAC1EC,aAAa,EACbC,wBAAwB,KACrB;EACH;EACA,MAAM;IACJC,kBAAkB;IAClBC,uBAAuB;IACvBC,sBAAsB;IACtBC,0BAA0B;IAC1BC,gCAAgC,EAAEC;EACpC,CAAC,GAAGT,sBAAsB,CAAC,CAAC;;EAE5B;EACA,MAAMU,cAAsE,GAC1Ed,kBAAkB,CAChB,CAAC;IAAEe,aAAa,EAAE;MAAEC;IAAE;EAAE,CAAC,EAAEC,OAAO,KAAK;IACrC;AACR;AACA;AACA;IACQ,IACET,kBAAkB,CAACU,KAAK,KAAKf,WAAW,CAACgB,QAAQ,IACjDX,kBAAkB,CAACU,KAAK,KAAKf,WAAW,CAACiB,WAAW,EACpD;MACAH,OAAO,CAACI,yBAAyB,GAAG,KAAK;IAC3C;;IAEA;AACR;AACA;AACA;IACQ,IAAIV,0BAA0B,CAACO,KAAK,KAAKpB,KAAK,CAACwB,MAAM,EAAE;MACrDL,OAAO,CAACI,yBAAyB,GAAG,IAAI;MACxCJ,OAAO,CAACM,qBAAqB,GAAGP,CAAC;IACnC;IAEA,IAAIP,uBAAuB,CAACS,KAAK,KAAKhB,gBAAgB,CAACsB,MAAM,EAAE;MAC7D,MAAMC,YAAY,GAAGR,OAAO,CAACI,yBAAyB,GACjDJ,OAAO,CAACM,qBAAqB,IAAI,CAAC,GACnC,CAAC;MACL;MACAxB,QAAQ,CAACO,aAAa,EAAE,CAAC,EAAEmB,YAAY,EAAE,KAAK,CAAC;MAC/ClB,wBAAwB,CAACW,KAAK,GAAGO,YAAY;MAC7C;IACF;EACF,CAAC,EACD,CACEnB,aAAa,EACbC,wBAAwB,EACxBE,uBAAuB,EACvBD,kBAAkB,CAEtB,CAAC;EACH,MAAMkB,iBAAyE,GAC7E1B,kBAAkB,CAChB,CAAC;IAAEe,aAAa,EAAE;MAAEC;IAAE;EAAE,CAAC,EAAEC,OAAO,KAAK;IACrCV,wBAAwB,CAACW,KAAK,GAAGF,CAAC;IAClCH,4BAA4B,CAACK,KAAK,GAAGF,CAAC;IACtCC,OAAO,CAACM,qBAAqB,GAAGP,CAAC;;IAEjC;AACR;AACA;AACA;IACQ,IACER,kBAAkB,CAACU,KAAK,KAAKf,WAAW,CAACgB,QAAQ,IACjDX,kBAAkB,CAACU,KAAK,KAAKf,WAAW,CAACiB,WAAW,IACpDJ,CAAC,GAAG,CAAC,EACL;MACAC,OAAO,CAACI,yBAAyB,GAAG,IAAI;IAC1C,CAAC,MAAM;MACLJ,OAAO,CAACI,yBAAyB,GAAG,KAAK;IAC3C;EACF,CAAC,EACD,CACEd,wBAAwB,EACxBC,kBAAkB,EAClBK,4BAA4B,CAEhC,CAAC;EACH,MAAMc,eAAuE,GAC3E3B,kBAAkB,CAChB,CAAC;IAAEe,aAAa,EAAE;MAAEC;IAAE;EAAE,CAAC,EAAEC,OAAO,KAAK;IACrC,IAAIR,uBAAuB,CAACS,KAAK,KAAKhB,gBAAgB,CAACsB,MAAM,EAAE;MAC7D,MAAMC,YAAY,GAAGR,OAAO,CAACI,yBAAyB,GACjDJ,OAAO,CAACM,qBAAqB,IAAI,CAAC,GACnC,CAAC;MACL;MACAxB,QAAQ,CAACO,aAAa,EAAE,CAAC,EAAEmB,YAAY,EAAE,KAAK,CAAC;MAC/ClB,wBAAwB,CAACW,KAAK,GAAGO,YAAY;MAC7C;IACF;IAEA,IAAIf,sBAAsB,CAACQ,KAAK,KAAKjB,eAAe,CAAC2B,OAAO,EAAE;MAC5DrB,wBAAwB,CAACW,KAAK,GAAGF,CAAC;MAClCH,4BAA4B,CAACK,KAAK,GAAGF,CAAC;IACxC;EACF,CAAC,EACD,CACEV,aAAa,EACbC,wBAAwB,EACxBG,sBAAsB,EACtBD,uBAAuB,EACvBI,4BAA4B,CAEhC,CAAC;EACH,MAAMgB,mBAA2E,GAC/E7B,kBAAkB,CAChB,CAAC;IAAEe,aAAa,EAAE;MAAEC;IAAE;EAAE,CAAC,EAAEC,OAAO,KAAK;IACrC,IAAIR,uBAAuB,CAACS,KAAK,KAAKhB,gBAAgB,CAACsB,MAAM,EAAE;MAC7D,MAAMC,YAAY,GAAGR,OAAO,CAACI,yBAAyB,GACjDJ,OAAO,CAACM,qBAAqB,IAAI,CAAC,GACnC,CAAC;MACL;MACAxB,QAAQ,CAACO,aAAa,EAAE,CAAC,EAAEmB,YAAY,EAAE,KAAK,CAAC;MAC/ClB,wBAAwB,CAACW,KAAK,GAAG,CAAC;MAClC;IACF;IAEA,IAAIR,sBAAsB,CAACQ,KAAK,KAAKjB,eAAe,CAAC2B,OAAO,EAAE;MAC5DrB,wBAAwB,CAACW,KAAK,GAAGF,CAAC;MAClCH,4BAA4B,CAACK,KAAK,GAAGF,CAAC;IACxC;EACF,CAAC,EACD,CACET,wBAAwB,EACxBD,aAAa,EACbI,sBAAsB,EACtBD,uBAAuB,EACvBI,4BAA4B,CAEhC,CAAC;EACH;;EAEA,OAAO;IACLC,cAAc;IACdY,iBAAiB;IACjBC,eAAe;IACfE;EACF,CAAC;AACH,CAAC", "ignoreList": []}