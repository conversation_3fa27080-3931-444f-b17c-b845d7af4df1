{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_reactNative", "_constants", "_hooks", "_utilities", "_styles", "_jsxRuntime", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "BottomSheetHostingContainerComponent", "containerHeight", "containerOffset", "topInset", "bottomInset", "shouldCalculateHeight", "detached", "style", "children", "containerRef", "useRef", "containerStyle", "useMemo", "styles", "container", "top", "bottom", "overflow", "handleLayoutEvent", "useStableCallback", "nativeEvent", "layout", "height", "value", "current", "measure", "_x", "_y", "_width", "_height", "_pageX", "pageY", "left", "right", "Math", "max", "WINDOW_HEIGHT", "StatusBar", "currentHeight", "__DEV__", "print", "component", "method", "category", "params", "jsx", "View", "ref", "pointerEvents", "onLayout", "undefined", "collapsable", "BottomSheetHostingContainer", "exports", "memo", "displayName"], "sourceRoot": "../../../../src", "sources": ["components/bottomSheetHostingContainer/BottomSheetHostingContainer.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAOA,IAAAE,UAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,UAAA,GAAAJ,OAAA;AACA,IAAAK,OAAA,GAAAL,OAAA;AAAkC,IAAAM,WAAA,GAAAN,OAAA;AAAA,SAAAO,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAT,wBAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAGlC,SAASW,oCAAoCA,CAAC;EAC5CC,eAAe;EACfC,eAAe;EACfC,QAAQ,GAAG,CAAC;EACZC,WAAW,GAAG,CAAC;EACfC,qBAAqB,GAAG,IAAI;EAC5BC,QAAQ;EACRC,KAAK;EACLC;AACgC,CAAC,EAAE;EACnC;EACA,MAAMC,YAAY,GAAG,IAAAC,aAAM,EAAO,IAAI,CAAC;EACvC;;EAEA;EACA,MAAMC,cAAc,GAAG,IAAAC,cAAO,EAC5B,MAAM,CACJL,KAAK,EACLM,cAAM,CAACC,SAAS,EAChB;IACEC,GAAG,EAAEZ,QAAQ;IACba,MAAM,EAAEZ,WAAW;IACnBa,QAAQ,EAAEX,QAAQ,GAAG,SAAS,GAAG;EACnC,CAAC,CACF,EACD,CAACC,KAAK,EAAED,QAAQ,EAAEH,QAAQ,EAAEC,WAAW,CACzC,CAAC;EACD;;EAEA;EACA,MAAMc,iBAAiB,GAAG,IAAAC,wBAAiB,EAAC,SAASD,iBAAiBA,CAAC;IACrEE,WAAW,EAAE;MACXC,MAAM,EAAE;QAAEC;MAAO;IACnB;EACiB,CAAC,EAAE;IACpBrB,eAAe,CAACsB,KAAK,GAAGD,MAAM;IAC9Bb,YAAY,CAACe,OAAO,EAAEC,OAAO,CAAC,CAACC,EAAE,EAAEC,EAAE,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,KAAK,KAAK;MACxE,IAAI,CAAC7B,eAAe,CAACqB,KAAK,EAAE;QAC1B;MACF;MACArB,eAAe,CAACqB,KAAK,GAAG;QACtBR,GAAG,EAAEgB,KAAK,IAAI,CAAC;QACfC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRjB,MAAM,EAAEkB,IAAI,CAACC,GAAG,CACd,CAAC,EACDC,wBAAa,IACV,CAACL,KAAK,IAAI,CAAC,IAAIT,MAAM,IAAIe,sBAAS,CAACC,aAAa,IAAI,CAAC,CAAC,CAC3D;MACF,CAAC;IACH,CAAC,CAAC;IAEF,IAAIC,OAAO,EAAE;MACX,IAAAC,gBAAK,EAAC;QACJC,SAAS,EAAE,6BAA6B;QACxCC,MAAM,EAAE,mBAAmB;QAC3BC,QAAQ,EAAE,QAAQ;QAClBC,MAAM,EAAE;UACNtB,MAAM;UACNP,GAAG,EAAEb,eAAe,CAACqB,KAAK,EAAER,GAAG;UAC/BiB,IAAI,EAAE9B,eAAe,CAACqB,KAAK,EAAES,IAAI;UACjCC,KAAK,EAAE/B,eAAe,CAACqB,KAAK,EAAEU,KAAK;UACnCjB,MAAM,EAAEd,eAAe,CAACqB,KAAK,EAAEP,MAAM;UACrCoB,aAAa,EAAbA;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF;;EAEA;EACA,oBACE,IAAAzD,WAAA,CAAAkE,GAAA,EAACvE,YAAA,CAAAwE,IAAI;IACHC,GAAG,EAAEtC,YAAa;IAClBuC,aAAa,EAAC,UAAU;IACxBC,QAAQ,EAAE5C,qBAAqB,GAAGa,iBAAiB,GAAGgC,SAAU;IAChE3C,KAAK,EAAEI,cAAe;IACtBwC,WAAW,EAAE,IAAK;IAAA3C,QAAA,EAEjBA;EAAQ,CACL,CAAC;EAET;AACF;AAEO,MAAM4C,2BAA2B,GAAAC,OAAA,CAAAD,2BAAA,gBAAG,IAAAE,WAAI,EAC7CtD,oCACF,CAAC;AACDoD,2BAA2B,CAACG,WAAW,GAAG,6BAA6B", "ignoreList": []}