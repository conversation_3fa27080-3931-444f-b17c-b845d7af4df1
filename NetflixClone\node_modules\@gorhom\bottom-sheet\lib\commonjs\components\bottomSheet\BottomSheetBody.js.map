{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_reactNative", "_reactNativeReanimated", "_hooks", "_styles", "_jsxRuntime", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "BottomSheetBodyComponent", "style", "children", "animatedIndex", "animatedPosition", "useBottomSheetInternal", "containerAnimatedStyle", "useAnimatedStyle", "opacity", "Platform", "OS", "transform", "translateY", "containerStyle", "useMemo", "styles", "container", "jsx", "View", "collapsable", "BottomSheetBody", "exports", "memo", "displayName"], "sourceRoot": "../../../../src", "sources": ["components/bottomSheet/BottomSheetBody.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,sBAAA,GAAAH,uBAAA,CAAAC,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAEA,IAAAI,OAAA,GAAAJ,OAAA;AAAkC,IAAAK,WAAA,GAAAL,OAAA;AAAA,SAAAM,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAR,wBAAAQ,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAOlC,SAASW,wBAAwBA,CAAC;EAAEC,KAAK;EAAEC;AAA+B,CAAC,EAAE;EAC3E;EACA,MAAM;IAAEC,aAAa;IAAEC;EAAiB,CAAC,GAAG,IAAAC,6BAAsB,EAAC,CAAC;EACpE;;EAEA;EACA,MAAMC,sBAAsB,GAAG,IAAAC,uCAAgB,EAC7C,OAAO;IACLC,OAAO,EAAEC,qBAAQ,CAACC,EAAE,KAAK,SAAS,IAAIP,aAAa,CAACf,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IACxEuB,SAAS,EAAE,CACT;MACEC,UAAU,EAAER,gBAAgB,CAAChB,GAAG,CAAC;IACnC,CAAC;EAEL,CAAC,CAAC,EACF,CAACgB,gBAAgB,EAAED,aAAa,CAClC,CAAC;EACD,MAAMU,cAAc,GAAG,IAAAC,cAAO,EAC5B,MAAM,CAACb,KAAK,EAAEc,cAAM,CAACC,SAAS,EAAEV,sBAAsB,CAAC,EACvD,CAACL,KAAK,EAAEK,sBAAsB,CAChC,CAAC;EACD;;EAEA,oBACE,IAAA3B,WAAA,CAAAsC,GAAA,EAACzC,sBAAA,CAAAU,OAAQ,CAACgC,IAAI;IAACjB,KAAK,EAAEY,cAAe;IAACM,WAAW,EAAE,IAAK;IAAAjB,QAAA,EACrDA;EAAQ,CACI,CAAC;AAEpB;AAEO,MAAMkB,eAAe,GAAAC,OAAA,CAAAD,eAAA,gBAAG,IAAAE,WAAI,EAACtB,wBAAwB,CAAC;AAC7DoB,eAAe,CAACG,WAAW,GAAG,iBAAiB", "ignoreList": []}