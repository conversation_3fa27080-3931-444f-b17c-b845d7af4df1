{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_reactNativeReanimated", "_interopRequireDefault", "_hooks", "_constants", "_BottomSheetDraggableScrollable", "_jsxRuntime", "e", "__esModule", "default", "_getRequireWildcardCache", "WeakMap", "r", "t", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "isWebkit", "navigator", "userAgent", "indexOf", "ScrollableContainer", "exports", "forwardRef", "nativeGesture", "ScrollableComponent", "animatedProps", "setContentSize", "onLayout", "rest", "ref", "isInitialContentHeightCaptured", "useRef", "animatedContentHeight", "useBottomSheetInternal", "renderScrollComponent", "useCallback", "props", "jsx", "ScrollView", "handleOnLayout", "event", "current", "INITIAL_CONTAINER_HEIGHT", "window", "requestAnimationFrame", "nativeEvent", "target", "clientHeight", "BottomSheetDraggableScrollable", "scrollableGesture", "children"], "sourceRoot": "../../../../src", "sources": ["components/bottomSheetScrollable/ScrollableContainer.web.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AAQA,IAAAC,sBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,UAAA,GAAAJ,OAAA;AACA,IAAAK,+BAAA,GAAAL,OAAA;AAAkF,IAAAM,WAAA,GAAAN,OAAA;AAAA,SAAAE,uBAAAK,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,yBAAAH,CAAA,6BAAAI,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAD,wBAAA,YAAAA,CAAAH,CAAA,WAAAA,CAAA,GAAAM,CAAA,GAAAD,CAAA,KAAAL,CAAA;AAAA,SAAAR,wBAAAQ,CAAA,EAAAK,CAAA,SAAAA,CAAA,IAAAL,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAE,OAAA,EAAAF,CAAA,QAAAM,CAAA,GAAAH,wBAAA,CAAAE,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAP,CAAA,UAAAM,CAAA,CAAAE,GAAA,CAAAR,CAAA,OAAAS,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAf,CAAA,oBAAAe,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAe,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAd,CAAA,EAAAe,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAf,CAAA,CAAAe,CAAA,YAAAN,CAAA,CAAAP,OAAA,GAAAF,CAAA,EAAAM,CAAA,IAAAA,CAAA,CAAAa,GAAA,CAAAnB,CAAA,EAAAS,CAAA,GAAAA,CAAA;AAUlF;AACA;AACA;AACA,MAAMW,QAAQ,GAAGA,CAAA,KAAM;EACrB;EACA,OAAOC,SAAS,CAACC,SAAS,CAACC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACnD,CAAC;AAEM,MAAMC,mBAAmB,GAAAC,OAAA,CAAAD,mBAAA,gBAAG,IAAAE,iBAAU,EAG3C,SAASF,mBAAmBA,CAC5B;EACEG,aAAa;EACbC,mBAAmB;EACnBC,aAAa;EACbC,cAAc;EACdC,QAAQ;EACR,GAAGC;AACL,CAAC,EACDC,GAAG,EACH;EACA;EACA,MAAMC,8BAA8B,GAAG,IAAAC,aAAM,EAAC,KAAK,CAAC;EACpD;;EAEA;EACA,MAAM;IAAEC;EAAsB,CAAC,GAAG,IAAAC,6BAAsB,EAAC,CAAC;EAC1D;;EAEA;EACA,MAAMC,qBAAqB,GAAG,IAAAC,kBAAW,EACtCC,KAAiD,iBAChD,IAAAzC,WAAA,CAAA0C,GAAA,EAAC/C,sBAAA,CAAAQ,OAAQ,CAACwC,UAAU;IAAA,GAAKF,KAAK;IAAEX,aAAa,EAAEA;EAAc,CAAE,CAChE,EACD,CAACA,aAAa,CAChB,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMc,cAAc,GAAG,IAAAJ,kBAAW,EAC/BK,KAAwB,IAAK;IAC5B,IAAIb,QAAQ,EAAE;MACZA,QAAQ,CAACa,KAAK,CAAC;IACjB;IAEA,IAAI,CAACV,8BAA8B,CAACW,OAAO,EAAE;MAC3CX,8BAA8B,CAACW,OAAO,GAAG,IAAI;MAC7C,IAAI,CAACzB,QAAQ,CAAC,CAAC,EAAE;QACf;MACF;;MAEA;AACR;AACA;MACQ,IAAIgB,qBAAqB,CAAC5B,GAAG,CAAC,CAAC,KAAKsC,mCAAwB,EAAE;QAC5D;MACF;MACA;MACAC,MAAM,CAACC,qBAAqB,CAAC,MAAM;QACjC;QACAlB,cAAc,CAACc,KAAK,CAACK,WAAW,CAACC,MAAM,CAACC,YAAY,CAAC;MACvD,CAAC,CAAC;IACJ;EACF,CAAC,EACD,CAACpB,QAAQ,EAAED,cAAc,EAAEM,qBAAqB,CAClD,CAAC;EACD;EACA,oBACE,IAAArC,WAAA,CAAA0C,GAAA,EAAC3C,+BAAA,CAAAsD,8BAA8B;IAACC,iBAAiB,EAAE1B,aAAc;IAAA2B,QAAA,eAC/D,IAAAvD,WAAA,CAAA0C,GAAA,EAACb,mBAAmB;MAClBK,GAAG,EAAEA,GAAI;MAAA,GACLD,IAAI;MACRD,QAAQ,EAAEY,cAAe;MACzBL,qBAAqB,EAAEA;IAAsB,CAC9C;EAAC,CAC4B,CAAC;AAErC,CAAC,CAAC", "ignoreList": []}