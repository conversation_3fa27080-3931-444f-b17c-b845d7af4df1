{"version": 3, "sources": ["Portal.tsx"], "names": ["PortalComponent", "name", "_providedName", "hostName", "handleOnMount", "_providedHandleOnMount", "handleOnUnmount", "_providedHandleOnUnmount", "handleOnUpdate", "_providedHandleOnUpdate", "children", "addPortal", "addUpdatePortal", "<PERSON><PERSON><PERSON><PERSON>", "handleOnMountRef", "handleOnUnmountRef", "handleOnUpdateRef", "current", "undefined", "Portal", "displayName"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AAGA,MAAMA,eAAe,GAAG,CAAC;AACvBC,EAAAA,IAAI,EAAEC,aADiB;AAEvBC,EAAAA,QAFuB;AAGvBC,EAAAA,aAAa,EAAEC,sBAHQ;AAIvBC,EAAAA,eAAe,EAAEC,wBAJM;AAKvBC,EAAAA,cAAc,EAAEC,uBALO;AAMvBC,EAAAA;AANuB,CAAD,KAOL;AACjB;AACA,QAAM;AAAEC,IAAAA,SAAS,EAAEC,eAAb;AAA8BC,IAAAA;AAA9B,MAA+C,0BAAUV,QAAV,CAArD,CAFiB,CAGjB;AAEA;;AACA,QAAMF,IAAI,GAAG,oBAAQ,MAAMC,aAAa,IAAI,wBAA/B,EAAyC,CAACA,aAAD,CAAzC,CAAb,CANiB,CAOjB;AAEA;;AACA,QAAMY,gBAAgB,GAAG,oBAAzB;AACA,QAAMC,kBAAkB,GAAG,oBAA3B;AACA,QAAMC,iBAAiB,GAAG,oBAA1B,CAZiB,CAajB;AAEA;;AACA,QAAMZ,aAAa,GAAG,wBAAY,MAAM;AACtC,QAAIC,sBAAJ,EAA4B;AAC1BA,MAAAA,sBAAsB,CAAC,MAAMO,eAAe,CAACX,IAAD,EAAOS,QAAP,CAAtB,CAAtB;AACD,KAFD,MAEO;AACLE,MAAAA,eAAe,CAACX,IAAD,EAAOS,QAAP,CAAf;AACD,KALqC,CAMtC;;AACD,GAPqB,EAOnB,CAACL,sBAAD,EAAyBO,eAAzB,CAPmB,CAAtB;AAQAE,EAAAA,gBAAgB,CAACG,OAAjB,GAA2Bb,aAA3B;AAEA,QAAME,eAAe,GAAG,wBAAY,MAAM;AACxC,QAAIC,wBAAJ,EAA8B;AAC5BA,MAAAA,wBAAwB,CAAC,MAAMM,YAAY,CAACZ,IAAD,CAAnB,CAAxB;AACD,KAFD,MAEO;AACLY,MAAAA,YAAY,CAACZ,IAAD,CAAZ;AACD,KALuC,CAMxC;;AACD,GAPuB,EAOrB,CAACM,wBAAD,EAA2BM,YAA3B,CAPqB,CAAxB;AAQAE,EAAAA,kBAAkB,CAACE,OAAnB,GAA6BX,eAA7B;AAEA,QAAME,cAAc,GAAG,wBAAY,MAAM;AACvC,QAAIC,uBAAJ,EAA6B;AAC3BA,MAAAA,uBAAuB,CAAC,MAAMG,eAAe,CAACX,IAAD,EAAOS,QAAP,CAAtB,CAAvB;AACD,KAFD,MAEO;AACLE,MAAAA,eAAe,CAACX,IAAD,EAAOS,QAAP,CAAf;AACD,KALsC,CAMvC;;AACD,GAPsB,EAOpB,CAACD,uBAAD,EAA0BG,eAA1B,EAA2CF,QAA3C,CAPoB,CAAvB;AAQAM,EAAAA,iBAAiB,CAACC,OAAlB,GAA4BT,cAA5B,CA5CiB,CA6CjB;AAEA;;AACA,wBAAU,MAAM;AAAA;;AACd,6BAAAM,gBAAgB,CAACG,OAAjB,qFAAAH,gBAAgB;AAChB,WAAO,MAAM;AAAA;;AACX,+BAAAC,kBAAkB,CAACE,OAAnB,qFAAAF,kBAAkB,EADP,CAGX;;AACAD,MAAAA,gBAAgB,CAACG,OAAjB,GAA2BC,SAA3B;AACAH,MAAAA,kBAAkB,CAACE,OAAnB,GAA6BC,SAA7B;AACAF,MAAAA,iBAAiB,CAACC,OAAlB,GAA4BC,SAA5B;AACD,KAPD;AAQD,GAVD,EAUG,EAVH;AAWA,wBAAU,MAAM;AAAA;;AACd,6BAAAF,iBAAiB,CAACC,OAAlB,qFAAAD,iBAAiB;AAClB,GAFD,EAEG,CAACN,QAAD,CAFH,EA3DiB,CA8DjB;;AAEA,SAAO,IAAP;AACD,CAxED;;AA0EO,MAAMS,MAAM,gBAAG,iBAAKnB,eAAL,CAAf;;AACPmB,MAAM,CAACC,WAAP,GAAqB,QAArB", "sourcesContent": ["import { memo, useCallback, useEffect, useMemo, useRef } from 'react';\nimport { nanoid } from 'nanoid/non-secure';\nimport { usePortal } from '../../hooks/usePortal';\nimport type { PortalProps } from './types';\n\nconst PortalComponent = ({\n  name: _providedName,\n  hostName,\n  handleOnMount: _providedHandleOnMount,\n  handleOnUnmount: _providedHandleOnUnmount,\n  handleOnUpdate: _providedHandleOnUpdate,\n  children,\n}: PortalProps) => {\n  //#region hooks\n  const { addPortal: addUpdatePortal, removePortal } = usePortal(hostName);\n  //#endregion\n\n  //#region variables\n  const name = useMemo(() => _providedName || nanoid(), [_providedName]);\n  //#endregion\n\n  //#region refs\n  const handleOnMountRef = useRef<Function>();\n  const handleOnUnmountRef = useRef<Function>();\n  const handleOnUpdateRef = useRef<Function>();\n  //#endregion\n\n  //#region callbacks\n  const handleOnMount = useCallback(() => {\n    if (_providedHandleOnMount) {\n      _providedHandleOnMount(() => addUpdatePortal(name, children));\n    } else {\n      addUpdatePortal(name, children);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [_providedHandleOnMount, addUpdatePortal]);\n  handleOnMountRef.current = handleOnMount;\n\n  const handleOnUnmount = useCallback(() => {\n    if (_providedHandleOnUnmount) {\n      _providedHandleOnUnmount(() => removePortal(name));\n    } else {\n      removePortal(name);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [_providedHandleOnUnmount, removePortal]);\n  handleOnUnmountRef.current = handleOnUnmount;\n\n  const handleOnUpdate = useCallback(() => {\n    if (_providedHandleOnUpdate) {\n      _providedHandleOnUpdate(() => addUpdatePortal(name, children));\n    } else {\n      addUpdatePortal(name, children);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [_providedHandleOnUpdate, addUpdatePortal, children]);\n  handleOnUpdateRef.current = handleOnUpdate;\n  //#endregion\n\n  //#region effects\n  useEffect(() => {\n    handleOnMountRef.current?.();\n    return () => {\n      handleOnUnmountRef.current?.();\n\n      // remove callbacks refs\n      handleOnMountRef.current = undefined;\n      handleOnUnmountRef.current = undefined;\n      handleOnUpdateRef.current = undefined;\n    };\n  }, []);\n  useEffect(() => {\n    handleOnUpdateRef.current?.();\n  }, [children]);\n  //#endregion\n\n  return null;\n};\n\nexport const Portal = memo(PortalComponent);\nPortal.displayName = 'Portal';\n"]}