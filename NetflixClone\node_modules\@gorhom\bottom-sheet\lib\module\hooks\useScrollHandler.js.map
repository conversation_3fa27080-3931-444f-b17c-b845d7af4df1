{"version": 3, "names": ["runOnJS", "useAnimatedRef", "useAnimatedScrollHandler", "useSharedValue", "workletNoop", "noop", "useScrollEventsHandlersDefault", "useScrollHandler", "useScrollEventsHandlers", "onScroll", "onScrollBeginDrag", "onScrollEndDrag", "scrollableRef", "scrollableContentOffsetY", "handleOnScroll", "handleOnBeginDrag", "handleOnEndDrag", "handleOnMomentumEnd", "handleOnMomentumBegin", "<PERSON><PERSON><PERSON><PERSON>", "event", "context", "nativeEvent", "onBeginDrag", "onEndDrag", "onMomentumBegin", "onMomentumEnd"], "sourceRoot": "../../../src", "sources": ["hooks/useScrollHandler.ts"], "mappings": ";;AAAA,SACEA,OAAO,EACPC,cAAc,EACdC,wBAAwB,EACxBC,cAAc,QACT,yBAAyB;AAEhC,SAASC,WAAW,IAAIC,IAAI,QAAQ,cAAc;AAClD,SAASC,8BAA8B,QAAQ,kCAAkC;AAEjF,OAAO,MAAMC,gBAAgB,GAAGA,CAC9BC,uBAAuB,GAAGF,8BAA8B,EACxDG,QAA0B,EAC1BC,iBAAmC,EACnCC,eAAiC,KAC9B;EACH;EACA,MAAMC,aAAa,GAAGX,cAAc,CAAa,CAAC;;EAElD;EACA,MAAMY,wBAAwB,GAAGV,cAAc,CAAS,CAAC,CAAC;;EAE1D;EACA,MAAM;IACJW,cAAc,GAAGT,IAAI;IACrBU,iBAAiB,GAAGV,IAAI;IACxBW,eAAe,GAAGX,IAAI;IACtBY,mBAAmB,GAAGZ,IAAI;IAC1Ba,qBAAqB,GAAGb;EAC1B,CAAC,GAAGG,uBAAuB,CAACI,aAAa,EAAEC,wBAAwB,CAAC;;EAEpE;EACA,MAAMM,aAAa,GAAGjB,wBAAwB,CAC5C;IACEO,QAAQ,EAAEA,CAACW,KAAK,EAAEC,OAAO,KAAK;MAC5BP,cAAc,CAACM,KAAK,EAAEC,OAAO,CAAC;MAE9B,IAAIZ,QAAQ,EAAE;QACZT,OAAO,CAACS,QAAQ,CAAC,CAAC;UAAEa,WAAW,EAAEF;QAAM,CAAC,CAAC;MAC3C;IACF,CAAC;IACDG,WAAW,EAAEA,CAACH,KAAK,EAAEC,OAAO,KAAK;MAC/BN,iBAAiB,CAACK,KAAK,EAAEC,OAAO,CAAC;MAEjC,IAAIX,iBAAiB,EAAE;QACrBV,OAAO,CAACU,iBAAiB,CAAC,CAAC;UAAEY,WAAW,EAAEF;QAAM,CAAC,CAAC;MACpD;IACF,CAAC;IACDI,SAAS,EAAEA,CAACJ,KAAK,EAAEC,OAAO,KAAK;MAC7BL,eAAe,CAACI,KAAK,EAAEC,OAAO,CAAC;MAE/B,IAAIV,eAAe,EAAE;QACnBX,OAAO,CAACW,eAAe,CAAC,CAAC;UAAEW,WAAW,EAAEF;QAAM,CAAC,CAAC;MAClD;IACF,CAAC;IACDK,eAAe,EAAEP,qBAAqB;IACtCQ,aAAa,EAAET;EACjB,CAAC,EACD,CACEH,cAAc,EACdC,iBAAiB,EACjBC,eAAe,EACfE,qBAAqB,EACrBD,mBAAmB,EACnBR,QAAQ,EACRC,iBAAiB,EACjBC,eAAe,CAEnB,CAAC;EAED,OAAO;IAAEQ,aAAa;IAAEP,aAAa;IAAEC;EAAyB,CAAC;AACnE,CAAC", "ignoreList": []}