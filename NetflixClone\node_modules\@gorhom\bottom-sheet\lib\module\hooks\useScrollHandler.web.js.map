{"version": 3, "names": ["useEffect", "useRef", "useSharedValue", "ANIMATION_STATE", "SCROLLABLE_STATE", "findNodeHandle", "useBottomSheetInternal", "useScrollHandler", "_", "onScroll", "scrollableRef", "scrollableContentOffsetY", "animatedScrollableState", "animatedAnimationState", "animatedScrollableContentOffsetY", "element", "current", "scrollOffset", "supportsPassive", "maybePrevent", "lastTouchY", "initialContentOffsetY", "shouldLockInitialPosition", "handleOnTouchStart", "event", "touches", "length", "scrollTop", "clientY", "handleOnTouchMove", "value", "LOCKED", "cancelable", "preventDefault", "touchY", "touchYDelta", "handleOnTouchEnd", "lockPosition", "scroll", "top", "left", "behavior", "handleOnScroll", "RUNNING", "Math", "max", "stopPropagation", "window", "addEventListener", "passive", "_e", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": "../../../src", "sources": ["hooks/useScrollHandler.web.ts"], "mappings": ";;AAAA,SAA0BA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,EAAEC,gBAAgB,QAAQ,cAAc;AAEhE,SAASC,cAAc,QAAQ,iCAAiC;AAChE,SAASC,sBAAsB,QAAQ,0BAA0B;AAOjE,OAAO,MAAMC,gBAAgB,GAAGA,CAACC,CAAQ,EAAEC,QAA0B,KAAK;EACxE;EACA,MAAMC,aAAa,GAAGT,MAAM,CAAa,IAAI,CAAC;EAC9C;;EAEA;EACA,MAAMU,wBAAwB,GAAGT,cAAc,CAAS,CAAC,CAAC;EAC1D;;EAEA;EACA,MAAM;IACJU,uBAAuB;IACvBC,sBAAsB;IACtBC;EACF,CAAC,GAAGR,sBAAsB,CAAC,CAAC;EAC5B;;EAEA;EACAN,SAAS,CAAC,MAAM;IACd;IACA,MAAMe,OAAO,GAAGV,cAAc,CAACK,aAAa,CAACM,OAAO,CAAQ;IAC5D,IAAIC,YAAY,GAAG,CAAC;IACpB,IAAIC,eAAe,GAAG,KAAK;IAC3B,IAAIC,YAAY,GAAG,KAAK;IACxB,IAAIC,UAAU,GAAG,CAAC;IAElB,IAAIC,qBAAqB,GAAG,CAAC;IAC7B,MAAMC,yBAAyB,GAAG,KAAK;IAEvC,SAASC,kBAAkBA,CAACC,KAAiB,EAAE;MAC7C,IAAIA,KAAK,CAACC,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QAC9B;MACF;MAEAL,qBAAqB,GAAGN,OAAO,CAACY,SAAS;MACzCP,UAAU,GAAGI,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACG,OAAO;MACrCT,YAAY,GAAGF,YAAY,IAAI,CAAC;IAClC;IAEA,SAASY,iBAAiBA,CAACL,KAAiB,EAAE;MAC5C,IAAIZ,uBAAuB,CAACkB,KAAK,KAAK1B,gBAAgB,CAAC2B,MAAM,IAAIP,KAAK,CAACQ,UAAU,EAAE;QACjF,OAAOR,KAAK,CAACS,cAAc,CAAC,CAAC;MAC/B;MAEA,IAAId,YAAY,EAAE;QAChBA,YAAY,GAAG,KAAK;QAEpB,MAAMe,MAAM,GAAGV,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACG,OAAO;QACvC,MAAMO,WAAW,GAAGD,MAAM,GAAGd,UAAU;QAEvC,IAAIe,WAAW,GAAG,CAAC,IAAIX,KAAK,CAACQ,UAAU,EAAE;UACvC,OAAOR,KAAK,CAACS,cAAc,CAAC,CAAC;QAC/B;MACF;MAEA,OAAO,IAAI;IACb;IAEA,SAASG,gBAAgBA,CAAA,EAAG;MAC1B,IAAIxB,uBAAuB,CAACkB,KAAK,KAAK1B,gBAAgB,CAAC2B,MAAM,EAAE;QAC7D,MAAMM,YAAY,GAAGf,yBAAyB,GACzCD,qBAAqB,IAAI,CAAC,GAC3B,CAAC;QACLN,OAAO,CAACuB,MAAM,CAAC;UACbC,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACF9B,wBAAwB,CAACmB,KAAK,GAAGO,YAAY;QAC7C;MACF;IACF;IAEA,SAASK,cAAcA,CAAClB,KAAiB,EAAE;MACzCP,YAAY,GAAGF,OAAO,CAACY,SAAS;MAEhC,IAAId,sBAAsB,CAACiB,KAAK,KAAK3B,eAAe,CAACwC,OAAO,EAAE;QAC5DhC,wBAAwB,CAACmB,KAAK,GAAGc,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE5B,YAAY,CAAC;QAC1DH,gCAAgC,CAACgB,KAAK,GAAGc,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE5B,YAAY,CAAC;MACpE;MAEA,IAAIA,YAAY,IAAI,CAAC,IAAIO,KAAK,CAACQ,UAAU,EAAE;QACzCR,KAAK,CAACS,cAAc,CAAC,CAAC;QACtBT,KAAK,CAACsB,eAAe,CAAC,CAAC;QACvB,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb;IAEA,IAAI;MACF;MACAC,MAAM,CAACC,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAE;QACpC;QACA;QACA,IAAIC,OAAOA,CAAA,EAAG;UACZ/B,eAAe,GAAG,IAAI;QACxB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOgC,EAAE,EAAE,CAAC;IAEdnC,OAAO,CAACiC,gBAAgB,CACtB,YAAY,EACZzB,kBAAkB,EAClBL,eAAe,GACX;MACE+B,OAAO,EAAE;IACX,CAAC,GACD,KACN,CAAC;IAEDlC,OAAO,CAACiC,gBAAgB,CACtB,WAAW,EACXnB,iBAAiB,EACjBX,eAAe,GACX;MACE+B,OAAO,EAAE;IACX,CAAC,GACD,KACN,CAAC;IAEDlC,OAAO,CAACiC,gBAAgB,CACtB,UAAU,EACVZ,gBAAgB,EAChBlB,eAAe,GACX;MACE+B,OAAO,EAAE;IACX,CAAC,GACD,KACN,CAAC;IAEDlC,OAAO,CAACiC,gBAAgB,CACtB,QAAQ,EACRN,cAAc,EACdxB,eAAe,GACX;MACE+B,OAAO,EAAE;IACX,CAAC,GACD,KACN,CAAC;IAED,OAAO,MAAM;MACX;MACAF,MAAM,CAACI,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;MACxCpC,OAAO,CAACoC,mBAAmB,CAAC,YAAY,EAAE5B,kBAAkB,CAAC;MAC7DR,OAAO,CAACoC,mBAAmB,CAAC,WAAW,EAAEtB,iBAAiB,CAAC;MAC3Dd,OAAO,CAACoC,mBAAmB,CAAC,UAAU,EAAEf,gBAAgB,CAAC;MACzDrB,OAAO,CAACoC,mBAAmB,CAAC,QAAQ,EAAET,cAAc,CAAC;IACvD,CAAC;EACH,CAAC,EAAE,CACD7B,sBAAsB,EACtBC,gCAAgC,EAChCF,uBAAuB,EACvBD,wBAAwB,CACzB,CAAC;EACF;;EAEA,OAAO;IACLyC,aAAa,EAAE3C,QAAQ;IACvBC,aAAa;IACbC;EACF,CAAC;AACH,CAAC", "ignoreList": []}