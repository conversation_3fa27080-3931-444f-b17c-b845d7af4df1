{"version": 3, "names": ["_reactNative", "require", "styles", "exports", "StyleSheet", "create", "container", "flexDirection", "position", "top", "left", "right"], "sourceRoot": "../../../../src", "sources": ["components/bottomSheet/styles.ts"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEO,MAAMC,MAAM,GAAAC,OAAA,CAAAD,MAAA,GAAGE,uBAAU,CAACC,MAAM,CAAC;EACtCC,SAAS,EAAE;IACTC,aAAa,EAAE,gBAAgB;IAC/BC,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE;EACT;AACF,CAAC,CAAC", "ignoreList": []}