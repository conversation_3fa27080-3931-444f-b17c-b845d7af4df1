import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  StatusBar,
  TouchableOpacity,
  Text,
  ActivityIndicator,
  Animated,
} from 'react-native';
import { useVideoPlayer, VideoView } from 'expo-video';
import { useEvent } from 'expo';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import * as ScreenOrientation from 'expo-screen-orientation';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { slothStyles, SLOTH_COLORS } from '../styles/sloth';

const PlayerScreen = ({ route, navigation }) => {
  const { item } = route.params;
  const insets = useSafeAreaInsets();

  // Player state
  const [controlsVisible, setControlsVisible] = useState(true);
  const [seekIndicator, setSeekIndicator] = useState(null);
  const [volume, setVolume] = useState(1.0);
  const [isMuted, setIsMuted] = useState(false);
  
  // Animation values
  const controlsOpacity = useRef(new Animated.Value(1)).current;
  const seekOpacity = useRef(new Animated.Value(0)).current;
  
  // Auto-hide controls timer
  const hideControlsTimer = useRef(null);
  
  // Create video source - for now using a demo video
  // In a real app, you would get the stream URL from your API
  const videoSource = 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4';
  
  // Create video player
  const player = useVideoPlayer(videoSource, (player) => {
    player.loop = false;
    player.timeUpdateEventInterval = 0.5; // Update every 500ms
    player.volume = volume;
    player.muted = isMuted;
    player.play();
  });
  
  // Listen to player events
  const { isPlaying } = useEvent(player, 'playingChange', { isPlaying: player.playing });
  const { currentTime, duration } = useEvent(player, 'timeUpdate', {
    currentTime: player.currentTime,
    duration: player.duration
  });
  const { status, error } = useEvent(player, 'statusChange', {
    status: player.status,
    error: player.error
  });

  // Listen for video end
  useEvent(player, 'playToEnd', () => {
    showControls();
    // Auto-save final position
    savePlaybackPosition(duration);
  });
  
  useEffect(() => {
    // Set landscape orientation for video player
    ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.LANDSCAPE);
    
    // Hide status bar
    StatusBar.setHidden(true);
    
    return () => {
      // Restore orientation and status bar when leaving
      ScreenOrientation.unlockAsync();
      StatusBar.setHidden(false);
    };
  }, []);
  
  useEffect(() => {
    // Auto-hide controls after 3 seconds
    if (controlsVisible && isPlaying) {
      hideControlsTimer.current = setTimeout(() => {
        hideControls();
      }, 3000);
    }
    
    return () => {
      if (hideControlsTimer.current) {
        clearTimeout(hideControlsTimer.current);
      }
    };
  }, [controlsVisible, isPlaying]);
  
  const showControls = () => {
    setControlsVisible(true);
    Animated.timing(controlsOpacity, {
      toValue: 1,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };
  
  const hideControls = () => {
    Animated.timing(controlsOpacity, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      setControlsVisible(false);
    });
  };
  
  const toggleControls = () => {
    if (controlsVisible) {
      hideControls();
    } else {
      showControls();
    }
  };
  
  const togglePlayPause = () => {
    if (isPlaying) {
      player.pause();
    } else {
      player.play();
    }
    showControls();
  };
  
  // Progress bar state for seeking
  const [progressBarWidth, setProgressBarWidth] = useState(0);

  const handleSeek = (progress) => {
    if (duration > 0) {
      const newTime = progress * duration;
      player.currentTime = newTime;
    }
  };

  const handleProgressBarTouch = (event) => {
    if (progressBarWidth > 0 && duration > 0) {
      const touchX = event.nativeEvent.locationX;
      const progress = Math.max(0, Math.min(1, touchX / progressBarWidth));
      handleSeek(progress);
      showControls();
    }
  };

  const toggleMute = () => {
    const newMutedState = !isMuted;
    setIsMuted(newMutedState);
    player.muted = newMutedState;
    showControls();
  };

  // Update player volume when state changes
  useEffect(() => {
    if (player) {
      player.volume = volume;
    }
  }, [volume, player]);

  useEffect(() => {
    if (player) {
      player.muted = isMuted;
    }
  }, [isMuted, player]);

  // Save playback position
  const savePlaybackPosition = async (position) => {
    try {
      const key = `playback_${item.id}_${item.media_type}`;
      await AsyncStorage.setItem(key, position.toString());
    } catch (error) {
      console.log('Error saving playback position:', error);
    }
  };

  // Load saved playback position
  const loadPlaybackPosition = async () => {
    try {
      const key = `playback_${item.id}_${item.media_type}`;
      const savedPosition = await AsyncStorage.getItem(key);
      if (savedPosition && parseFloat(savedPosition) > 10) { // Only restore if more than 10 seconds
        player.currentTime = parseFloat(savedPosition);
      }
    } catch (error) {
      console.log('Error loading playback position:', error);
    }
  };

  // Load saved position when player is ready
  useEffect(() => {
    if (status === 'readyToPlay') {
      loadPlaybackPosition();
    }
  }, [status]);

  // Save position every 10 seconds
  useEffect(() => {
    if (currentTime > 0 && Math.floor(currentTime) % 10 === 0) {
      savePlaybackPosition(currentTime);
    }
  }, [Math.floor(currentTime)]);
  
  const seekBy = (seconds) => {
    player.seekBy(seconds);
    setSeekIndicator(seconds > 0 ? 'forward' : 'backward');
    
    // Show seek indicator
    Animated.sequence([
      Animated.timing(seekOpacity, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.delay(500),
      Animated.timing(seekOpacity, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
    
    showControls();
  };
  
  const formatTime = (timeInSeconds) => {
    if (!timeInSeconds || timeInSeconds < 0) return '0:00';
    
    const hours = Math.floor(timeInSeconds / 3600);
    const minutes = Math.floor((timeInSeconds % 3600) / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };
  
  const getTitle = () => {
    if (item.media_type === 'tv' && item.episodeTitle) {
      return `${item.name} - S${item.season}E${item.episode} - ${item.episodeTitle}`;
    }
    return item.title || item.name;
  };
  
  const handleBack = () => {
    player.pause();
    navigation.goBack();
  };

  const handleReplay = () => {
    player.replay();
    showControls();
  };
  
  if (status === 'loading') {
    return (
      <View style={slothStyles.playerLoadingContainer}>
        <ActivityIndicator size="large" color={SLOTH_COLORS.primary} />
        <Text style={[slothStyles.timecodeText, { marginTop: 10 }]}>Loading...</Text>
      </View>
    );
  }

  if (status === 'error') {
    return (
      <View style={slothStyles.playerLoadingContainer}>
        <Ionicons name="alert-circle" size={48} color={SLOTH_COLORS.primary} />
        <Text style={[slothStyles.timecodeText, { marginTop: 10, textAlign: 'center' }]}>
          Error loading video
        </Text>
        <Text style={[slothStyles.timecodeText, { marginTop: 5, fontSize: 12, opacity: 0.7 }]}>
          {error?.message || 'Unknown error occurred'}
        </Text>
        <TouchableOpacity
          style={[slothStyles.detailPlayButton, { marginTop: 20, paddingHorizontal: 30 }]}
          onPress={handleBack}
        >
          <Text style={slothStyles.detailPlayButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  const progress = duration > 0 ? currentTime / duration : 0;
  
  return (
    <View style={slothStyles.playerContainer}>
      <StatusBar hidden />
      
      {/* Video Player */}
      <View style={slothStyles.playerVideoView}>
        <VideoView
          style={slothStyles.playerVideoView}
          player={player}
          allowsFullscreen={false}
          nativeControls={false}
          contentFit="contain"
        />

        {/* Touch overlay for controls */}
        <TouchableOpacity
          style={slothStyles.playerVideoView}
          activeOpacity={1}
          onPress={toggleControls}
        />

        {/* Loading indicator when buffering */}
        {status === 'loading' && (
          <View style={slothStyles.playerLoadingContainer}>
            <ActivityIndicator size="large" color={SLOTH_COLORS.primary} />
          </View>
        )}
      </View>
      
      {/* Seek Indicator */}
      <Animated.View 
        style={[slothStyles.seekIndicator, { opacity: seekOpacity }]}
        pointerEvents="none"
      >
        <View style={slothStyles.seekIndicatorIcon}>
          <Ionicons 
            name={seekIndicator === 'forward' ? 'play-forward' : 'play-back'} 
            size={32} 
            color={SLOTH_COLORS.white} 
          />
        </View>
      </Animated.View>
      
      {/* Controls Overlay */}
      {controlsVisible && (
        <Animated.View 
          style={[slothStyles.controlsOverlay, { opacity: controlsOpacity }]}
          pointerEvents={controlsVisible ? 'auto' : 'none'}
        >
          {/* Gradient Overlays */}
          <LinearGradient
            colors={['rgba(0,0,0,0.7)', 'transparent']}
            style={[slothStyles.gradientOverlay, { height: 100 }]}
            pointerEvents="none"
          />
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.7)']}
            style={[slothStyles.gradientOverlay, { top: 'auto', bottom: 0, height: 120 }]}
            pointerEvents="none"
          />
          
          {/* Top Controls */}
          <View style={[slothStyles.topControls, { paddingTop: insets.top + 10 }]}>
            <TouchableOpacity style={slothStyles.playerIconButton} onPress={handleBack}>
              <Ionicons name="arrow-back" size={24} color={SLOTH_COLORS.white} />
            </TouchableOpacity>

            <Text style={slothStyles.playerTitle} numberOfLines={1}>
              {getTitle()}
            </Text>

            <TouchableOpacity style={slothStyles.playerIconButton} onPress={toggleMute}>
              <Ionicons
                name={isMuted ? 'volume-mute' : 'volume-high'}
                size={24}
                color={SLOTH_COLORS.white}
              />
            </TouchableOpacity>
          </View>
          
          {/* Center Controls */}
          <View style={slothStyles.centerControls}>
            {duration > 0 && currentTime >= duration - 1 ? (
              // Show replay button when video has ended
              <TouchableOpacity onPress={handleReplay}>
                <Ionicons name="refresh" size={50} color={SLOTH_COLORS.white} />
              </TouchableOpacity>
            ) : (
              <>
                <TouchableOpacity onPress={() => seekBy(-10)}>
                  <Ionicons name="play-back" size={40} color={SLOTH_COLORS.white} />
                </TouchableOpacity>

                <TouchableOpacity onPress={togglePlayPause}>
                  <Ionicons
                    name={isPlaying ? 'pause' : 'play'}
                    size={50}
                    color={SLOTH_COLORS.white}
                  />
                </TouchableOpacity>

                <TouchableOpacity onPress={() => seekBy(10)}>
                  <Ionicons name="play-forward" size={40} color={SLOTH_COLORS.white} />
                </TouchableOpacity>
              </>
            )}
          </View>
          
          {/* Bottom Controls */}
          <View style={[slothStyles.bottomControls, { paddingBottom: insets.bottom + 20 }]}>
            {/* Progress Bar */}
            <TouchableOpacity
              style={slothStyles.sliderContainer}
              activeOpacity={1}
              onPress={handleProgressBarTouch}
              onLayout={(event) => {
                setProgressBarWidth(event.nativeEvent.layout.width);
              }}
            >
              <View style={slothStyles.sliderTrack}>
                <View
                  style={[
                    slothStyles.sliderProgress,
                    { width: `${progress * 100}%` }
                  ]}
                />
                {/* Thumb */}
                <View
                  style={[
                    slothStyles.sliderThumb,
                    { left: `${progress * 100}%`, marginLeft: -7 }
                  ]}
                />
              </View>
            </TouchableOpacity>
            
            {/* Time Codes */}
            <View style={slothStyles.timecodeContainer}>
              <Text style={slothStyles.timecodeText}>
                {formatTime(currentTime)}
              </Text>
              <Text style={slothStyles.timecodeText}>
                {formatTime(duration)}
              </Text>
            </View>
          </View>
        </Animated.View>
      )}
    </View>
  );
};

export default PlayerScreen;
