import React, { useState, useEffect, useRef, MouseEvent } from 'react';
import Hls from 'hls.js';
import { Play, Pause, Volume2, VolumeX, Settings, Expand, Shrink, Captions, Check, RotateCcw, RotateCw, Plus, Minus, RefreshCw } from 'lucide-react';

// --- INTERFACES & TYPES ---
interface Stream { name: string; url: string; }
interface ApiSubtitle { lang: string; url: string; }
interface ProcessedSubtitle { name: string; url: string; language: string; }
interface ParsedCue {
  start: number;
  end: number;
  text: string;
}

interface CustomVideoPlayerProps {
  streams: Stream[];
  subtitles?: ApiSubtitle[];
  onTimeUpdate?: (currentTime: number, duration: number) => void;
  startTime?: number;
}

// --- UTILITY FUNCTIONS ---
const formatTime = (timeInSeconds: number) => {
  if (isNaN(timeInSeconds) || timeInSeconds < 0) return '00:00';
  const floor = Math.floor;
  const hours = floor(timeInSeconds / 3600);
  const minutes = floor((timeInSeconds % 3600) / 60);
  const seconds = floor(timeInSeconds % 60);
  const pad = (num: number) => num.toString().padStart(2, '0');
  return hours > 0 ? `${pad(hours)}:${pad(minutes)}:${pad(seconds)}` : `${pad(minutes)}:${pad(seconds)}`;
};

const parseVTT = (vttText: string): ParsedCue[] => {
  // Normalize line endings and split into lines, then filter out completely empty lines.
  const lines = vttText.trim().replace(/\r/g, '').split('\n').filter(line => line.trim() !== '');
  const cues: ParsedCue[] = [];
  let i = 0;

  const parseTimeToSeconds = (time: string): number => {
    if (!time) return 0;
    const parts = time.split(':');
    const secondsParts = parts[parts.length - 1].split('.');
    let seconds = 0;
    try {
      if (parts.length === 3) { // HH:MM:SS.ms (e.g., 00:01:05.123)
        seconds += parseInt(parts[0], 10) * 3600; // Hours
        seconds += parseInt(parts[1], 10) * 60;   // Minutes
      } else { // MM:SS.ms (e.g., 01:05.123)
        seconds += parseInt(parts[0], 10) * 60;   // Minutes
      }
      seconds += parseInt(secondsParts[0], 10); // Seconds
      if (secondsParts[1]) {
        seconds += parseInt(secondsParts[1], 10) / 1000; // Milliseconds
      }
    } catch(e) {
      console.error("VTT parseTime Error:", time, e);
      return 0;
    }
    return seconds;
  };

  // Skip the WEBVTT header if present
  if (lines[0] && lines[0].startsWith('WEBVTT')) {
    i++;
  }

  while (i < lines.length) {
    // Skip comment lines or any lines that are not part of a cue (e.g., cue identifiers)
    if (lines[i].startsWith('NOTE') || !lines[i].includes('-->')) {
      i++;
      continue;
    }

    // Found a timestamp line
    if (lines[i].includes('-->')) {
      const timeLine = lines[i].split(' --> ');
      const start = parseTimeToSeconds(timeLine[0]);
      const end = parseTimeToSeconds(timeLine[1].split(' ')[0]); // Handle potential trailing settings like 'align:middle'

      let textLines: string[] = [];
      i++;
      // Collect text lines until we hit another timestamp line or the end of the file
      while (i < lines.length && !lines[i].includes('-->') && !lines[i].startsWith('WEBVTT') && !lines[i].startsWith('NOTE')) {
        textLines.push(lines[i].trim());
        i++;
      }
      cues.push({ start, end, text: textLines.join('\n') });
    } else {
        // Should not typically hit this if filtering and checks above are robust, but as a fallback
        i++;
    }
  }
  return cues;
};


const CustomVideoPlayer: React.FC<CustomVideoPlayerProps> = ({ streams, subtitles = [], onTimeUpdate, startTime = 0 }) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerContainerRef = useRef<HTMLDivElement>(null);
  const hlsRef = useRef<Hls | null>(null);
  const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isSeekingRef = useRef(false);
  const seekIndicatorTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const hasSetInitialTime = useRef(false);

  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [volume, setVolume] = useState(1);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [areControlsVisible, setAreControlsVisible] = useState(true);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [currentStreamUrl, setCurrentStreamUrl] = useState<string>(streams[0]?.url || '');
  const [isBuffering, setIsBuffering] = useState(false);
  const [currentSubtitle, setCurrentSubtitle] = useState<string>('off');
  const [seekIndicator, setSeekIndicator] = useState<'forward' | 'backward' | null>(null);
  const [subtitleOffset, setSubtitleOffset] = useState(0);
  const [parsedCues, setParsedCues] = useState<ParsedCue[]>([]);
  const [activeCueText, setActiveCueText] = useState('');
  const [isSubtitleSettingsOpen, setIsSubtitleSettingsOpen] = useState(false);
  
  const [processedSubtitles, setProcessedSubtitles] = useState<ProcessedSubtitle[]>([]);
  useEffect(() => {
    // --- FIX: Logic to handle subtitle URL directly or via proxy ---
    const processed = subtitles.map(sub => {
      const sorastreamBaseUrl = 'https://sorastream-five.vercel.app';
      let finalUrl = sub.url; // Start with the raw URL from the API

      // Check if the URL is already a direct path or full URL from sorastream-five.vercel.app
      if (sub.url.startsWith('/subtitles/')) {
          // If it's a relative path like "/subtitles/...", construct the full direct URL
          finalUrl = `${sorastreamBaseUrl}${sub.url}`;
          console.log(`Subtitle URL (relative path): Using direct URL ${finalUrl}`);
      } else if (sub.url.startsWith(sorastreamBaseUrl)) {
          // If it's already a full direct URL, use it as is
          finalUrl = sub.url;
          console.log(`Subtitle URL (full direct): Using direct URL ${finalUrl}`);
      } else {
          // Otherwise, assume it's an external URL or requires the proxy for CORS/security
          finalUrl = `https://sora-proxy-rho.vercel.app/api/proxy?target=${encodeURIComponent(sub.url)}`;
          console.log(`Subtitle URL (external/other): Using proxy URL ${finalUrl}`);
      }

      return {
        name: sub.lang,
        language: sub.lang.slice(0, 2).toLowerCase(),
        url: finalUrl // Use the determined final URL
      };
    });
    setProcessedSubtitles(processed);
  }, [subtitles]);
  
  useEffect(() => {
    hasSetInitialTime.current = false; 
    setCurrentTime(startTime || 0);
  }, [currentStreamUrl, startTime]);

  useEffect(() => {
    const selectedSub = processedSubtitles.find(sub => sub.name === currentSubtitle);
    
    if (!selectedSub || !selectedSub.url) {
      console.log('Subtitle selection is OFF or URL not found. Clearing parsed cues.');
      setParsedCues([]);
      setActiveCueText('');
      return;
    }
    
    console.log(`Attempting to fetch subtitle for: ${currentSubtitle} from URL: ${selectedSub.url}`);

    fetch(selectedSub.url)
      .then(res => {
        if (!res.ok) {
          console.error(`Subtitle fetch HTTP error! status: ${res.status} for URL: ${selectedSub.url}`);
          throw new Error(`HTTP error! status: ${res.status}`);
        }
        return res.text();
      })
      .then(text => {
        // Basic check to see if the fetched content resembles VTT
        if (!text.includes('WEBVTT') && !text.includes('-->')) {
             console.warn('Fetched subtitle text does not seem to be a valid VTT format (missing "WEBVTT" or "-->"). Content Preview:', text.substring(0, 500));
        }

        const cues = parseVTT(text);
        if (cues.length === 0) {
            console.warn(`No cues were parsed for subtitle: ${currentSubtitle}. Raw text length: ${text.length}.`);
        } else {
            console.log(`Successfully parsed ${cues.length} cues for ${currentSubtitle}. First cue:`, cues[0]);
        }
        setParsedCues(cues);
      })
      .catch(err => {
        console.error(`Failed to fetch or parse subtitles for ${currentSubtitle}:`, err);
        setParsedCues([]); // Clear cues on error
      });

  }, [currentSubtitle, processedSubtitles]);

  useEffect(() => {
    if (!currentStreamUrl || !videoRef.current) return;
    const video = videoRef.current;
    
    if (hlsRef.current) { hlsRef.current.destroy(); }
    const hls = new Hls();
    hlsRef.current = hls;
    const proxyUrl = `https://sora-proxy-rho.vercel.app/api/proxy?target=${encodeURIComponent(currentStreamUrl)}`;
    
    hls.loadSource(proxyUrl);
    hls.attachMedia(video);
    if (!Hls.isSupported() && video.canPlayType('application/vnd.apple.mpegurl')) { video.src = proxyUrl; }
    return () => { if (hlsRef.current) hlsRef.current.destroy(); };
  }, [currentStreamUrl]);
  
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);
    const handleDurationChange = () => setDuration(video.duration);
    const handleVolumeChange = () => { setIsMuted(video.muted); setVolume(video.volume) };
    const handleWaiting = () => { if (!isSeekingRef.current) setIsBuffering(true); };
    const handleSeeked = () => { isSeekingRef.current = false; };
    const handleSeeking = () => { isSeekingRef.current = true; };
    
    const handleCanPlay = () => {
        setIsBuffering(false);
        if (video && startTime && !hasSetInitialTime.current) {
            if (startTime > 1 && startTime < video.duration) {
                video.currentTime = startTime;
            }
            hasSetInitialTime.current = true;
        }
    };

    const handleTimeUpdate = () => {
        if (!isSeekingRef.current && video) {
            setCurrentTime(video.currentTime);
            if (onTimeUpdate) {
              onTimeUpdate(video.currentTime, video.duration);
            }
            // Update active subtitle cue based on video time + offset
            const nowWithOffset = video.currentTime + subtitleOffset;
            const activeCue = parsedCues.find(cue => nowWithOffset >= cue.start && nowWithOffset <= cue.end);
            setActiveCueText(activeCue ? activeCue.text : '');
        }
    };

    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);
    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('durationchange', handleDurationChange);
    video.addEventListener('volumechange', handleVolumeChange);
    video.addEventListener('waiting', handleWaiting);
    video.addEventListener('canplay', handleCanPlay);
    video.addEventListener('seeking', handleSeeking);
    video.addEventListener('seeked', handleSeeked);

    return () => {
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('durationchange', handleDurationChange);
      video.removeEventListener('volumechange', handleVolumeChange);
      video.removeEventListener('waiting', handleWaiting);
      video.removeEventListener('canplay', handleCanPlay);
      video.removeEventListener('seeking', handleSeeking);
      video.removeEventListener('seeked', handleSeeked);
    };
  }, [onTimeUpdate, startTime, parsedCues, subtitleOffset]); // Dependencies for timeUpdate

  useEffect(() => {
    const handleFullscreenChange = () => setIsFullscreen(!!document.fullscreenElement);
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  const hideControls = () => {
    if (isPlaying) {
        setAreControlsVisible(false);
        setIsSettingsOpen(false);
        setIsSubtitleSettingsOpen(false); // Hide subtitle settings too
    }
  };

  const showControls = () => {
    setAreControlsVisible(true);
    if (controlsTimeoutRef.current) clearTimeout(controlsTimeoutRef.current);
    controlsTimeoutRef.current = setTimeout(hideControls, 3000);
  };

  const togglePlayPause = () => {
    if (isSettingsOpen || isSubtitleSettingsOpen) { // Close menus on central click
      setIsSettingsOpen(false);
      setIsSubtitleSettingsOpen(false);
      return;
    }
    videoRef.current && (isPlaying ? videoRef.current.pause() : videoRef.current.play());
  };
  const toggleMute = () => { if (videoRef.current) videoRef.current.muted = !videoRef.current.muted; };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    if (videoRef.current) {
      videoRef.current.volume = newVolume;
      videoRef.current.muted = newVolume === 0;
    }
  };

  const handleProgressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!videoRef.current) return;
    const newTime = parseFloat(e.target.value);
    setCurrentTime(newTime);
    videoRef.current.currentTime = newTime;
  };
  
  const handleQualityChange = (url: string) => {
    const wasPlaying = isPlaying;
    const currentTimeSnapshot = videoRef.current?.currentTime || 0;
    
    setCurrentStreamUrl(url);
    setIsSettingsOpen(false);
    setIsBuffering(true); // Show buffering state during quality change
    setTimeout(() => {
      if(videoRef.current) {
        videoRef.current.currentTime = currentTimeSnapshot;
        if(wasPlaying) videoRef.current.play();
      }
    }, 500);
  };

  const toggleFullscreen = () => {
    if (!playerContainerRef.current) return;
    if (!isFullscreen) playerContainerRef.current.requestFullscreen();
    else document.exitFullscreen();
  };

  const handleSeek = (amount: number, event: MouseEvent) => {
    event.stopPropagation();
    if (videoRef.current) {
      const newTime = Math.max(0, Math.min(duration, videoRef.current.currentTime + amount));
      videoRef.current.currentTime = newTime;
      setCurrentTime(newTime); 

      setSeekIndicator(amount > 0 ? 'forward' : 'backward');
      if (seekIndicatorTimeoutRef.current) {
        clearTimeout(seekIndicatorTimeoutRef.current);
      }
      seekIndicatorTimeoutRef.current = setTimeout(() => {
        setSeekIndicator(null);
      }, 600);
    }
  };

  const handleContainerClick = (e: MouseEvent<HTMLDivElement>) => {
    if (e.target === playerContainerRef.current) {
        togglePlayPause();
    }
  };

  const changeSubtitleOffset = (amount: number) => {
    setSubtitleOffset(prev => parseFloat((prev + amount).toFixed(2))); // Keep 2 decimal places
  };
  
  const resyncSubtitles = () => {
    if (!videoRef.current || parsedCues.length === 0) {
      console.warn("Cannot resync: video not ready or no subtitles loaded.");
      return;
    }
    const videoTime = videoRef.current.currentTime;
    // Find the cue that *should* be active at the current video time, ignoring current offset
    const currentCue = parsedCues.find(cue => videoTime >= cue.start && videoTime <= cue.end);
    
    if (currentCue) {
      // Calculate the new offset needed to make the current cue's start time match videoTime
      const newOffset = currentCue.start - videoTime;
      setSubtitleOffset(parseFloat(newOffset.toFixed(2)));
      console.log(`Subtitles resynced. Video time: ${videoTime.toFixed(2)}s, Cue start: ${currentCue.start.toFixed(2)}s, New offset: ${newOffset.toFixed(2)}s`);
    } else {
        // If no cue is currently active, try to find the next upcoming cue
        const nextCue = parsedCues.find(cue => videoTime < cue.start);
        if (nextCue) {
            const newOffset = nextCue.start - videoTime;
            setSubtitleOffset(parseFloat(newOffset.toFixed(2)));
            console.log(`Resyncing to next cue. Video time: ${videoTime.toFixed(2)}s, Next cue start: ${nextCue.start.toFixed(2)}s, New offset: ${newOffset.toFixed(2)}s`);
        } else {
            console.warn("No active or upcoming cue found for resync.");
        }
    }
  };

  const progressPercentage = duration ? (currentTime / duration) * 100 : 0;

  return (
    <div 
      ref={playerContainerRef} 
      className="relative w-full aspect-[16/9] bg-gradient-to-br from-slate-900 to-slate-800 text-white rounded-2xl overflow-hidden shadow-2xl group"
      onMouseMove={showControls}
      onMouseLeave={hideControls}
      onClick={handleContainerClick}
    >
      <video 
        ref={videoRef} 
        className="w-full h-full object-cover" 
        onClick={(e) => { e.stopPropagation(); togglePlayPause(); }}
        crossOrigin="anonymous"
        autoPlay
        playsInline
      />
      
      {activeCueText && (
        <div className="absolute bottom-20 md:bottom-24 w-full text-center px-4 pointer-events-none z-40"> {/* Z-INDEX Increased to 40 */}
            <p 
                className="text-lg md:text-2xl font-bold p-2 bg-black/50 rounded-lg inline-block" // Added inline-block
                style={{ textShadow: '2px 2px 4px #000000' }}
                dangerouslySetInnerHTML={{ __html: activeCueText.replace(/\n/g, '<br />') }}
            />
        </div>
      )}

      {isBuffering && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/20 backdrop-blur-sm z-30">
          <div className="w-12 h-12 border-4 border-white/20 border-t-white rounded-full animate-spin"></div>
        </div>
      )}

      {/* Central Controls - z-20 so it's on top of video, below subtitles */}
      <div 
        className={`absolute inset-0 flex items-center justify-center gap-8 md:gap-12 z-20 transition-opacity duration-300 pointer-events-none ${
          areControlsVisible || !isPlaying ? 'opacity-100' : 'opacity-0'
        }`}
      >
        <button 
          onClick={(e) => handleSeek(-10, e)} 
          className="p-3 bg-white/10 backdrop-blur-md border border-white/20 rounded-full pointer-events-auto hover:bg-white/20 hover:scale-110 active:scale-95 transition-all duration-200 ease-out"
        >
          <RotateCcw size={28} className="text-white" />
        </button>

        {!isBuffering && ( // Only show play/pause if not buffering
          <button 
            onClick={(e) => { e.stopPropagation(); togglePlayPause(); }} 
            className="group/play p-4 md:p-6 bg-white/10 backdrop-blur-md border border-white/20 rounded-full pointer-events-auto hover:bg-white/20 hover:scale-110 active:scale-95 transition-all duration-200 ease-out"
          >
            {isPlaying ? 
              <Pause size={48} className="text-white transition-transform duration-300" /> :
              <Play size={48} className="text-white ml-2 group-hover/play:scale-110 transition-transform duration-300" />
            }
          </button>
        )}
        
        <button 
          onClick={(e) => handleSeek(10, e)} 
          className="p-3 bg-white/10 backdrop-blur-md border border-white/20 rounded-full pointer-events-auto hover:bg-white/20 hover:scale-110 active:scale-95 transition-all duration-200 ease-out"
        >
          <RotateCw size={28} className="text-white" />
        </button>
      </div>

      {/* Seek Indicator */}
      <div className={`absolute inset-0 flex items-center justify-center transition-all duration-500 pointer-events-none z-20 ${seekIndicator ? 'opacity-100' : 'opacity-0'}`}>
          <div className={`absolute top-1/2 -translate-y-1/2 flex items-center gap-2 bg-black/50 backdrop-blur-sm p-4 rounded-full transition-all duration-300 ${seekIndicator === 'forward' ? 'right-8' : 'left-8'}`}>
            {seekIndicator === 'forward' ? <RotateCw size={28} className="text-white"/> : <RotateCcw size={28} className="text-white"/>}
            <span className='text-white font-mono text-lg'>{seekIndicator === 'forward' ? '+10s' : '-10s'}</span>
          </div>
      </div>
      
      {/* Control Bar Overlay - z-20 for controls, its parent div has pointer-events-none */}
      <div 
        className={`absolute inset-0 transition-all duration-300 ease-out z-20 pointer-events-none ${areControlsVisible ? 'opacity-100' : 'opacity-0'}`}
      >
        <div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-black/80 via-black/40 to-transparent" />
        <div 
            className="absolute bottom-0 left-0 right-0 p-3 md:p-6 bg-gradient-to-t from-black/90 via-black/60 to-transparent pointer-events-auto"
            onClick={(e) => e.stopPropagation()}
        >
          <div className="relative mb-3 md:mb-4 group/progress">
            <div className="h-1.5 bg-white/20 rounded-full overflow-hidden backdrop-blur-sm group-hover/progress:h-2 transition-all duration-200">
              <div className="h-full bg-gradient-to-r from-red-500 to-red-400 rounded-full" style={{ width: `${progressPercentage}%` }} />
            </div>
            <input type="range" min={0} max={duration || 0} value={currentTime} onInput={handleProgressChange} onChange={handleProgressChange} className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"/>
            <div className="absolute top-1/2 w-4 h-4 bg-red-500 rounded-full -translate-y-1/2 -translate-x-2 opacity-0 group-hover/progress:opacity-100 transition-opacity duration-300 shadow-lg" style={{ left: `${progressPercentage}%` }}/>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 md:gap-6">
              <button onClick={togglePlayPause} className="p-2 hover:bg-white/10 rounded-full transition-all duration-300">{isPlaying ? <Pause size={24} /> : <Play size={24} />}</button>
              <div className="flex items-center group/volume">
                <button onClick={toggleMute} className="p-2 hover:bg-white/10 rounded-full transition-all duration-300">{isMuted || volume === 0 ? <VolumeX size={24} /> : <Volume2 size={24} />}</button>
                <div className="w-0 group-hover/volume:w-24 sm:w-24 transition-all duration-300 overflow-hidden">
                    <input type="range" min={0} max={1} step={0.05} value={isMuted ? 0 : volume} onChange={handleVolumeChange} className="w-24 h-1 bg-white/20 rounded-full appearance-none cursor-pointer range-thumb" style={{ backgroundSize: `${isMuted ? 0 : volume * 100}% 100%` }} />
                </div>
              </div>
              <div className="bg-black/30 backdrop-blur-sm px-3 py-1.5 rounded-lg text-sm font-mono border border-white/10">{formatTime(currentTime)} / {formatTime(duration)}</div>
            </div>

            <div className="flex items-center gap-1 md:gap-4">
              {processedSubtitles.length > 0 && (
                <div className="relative">
                  <button 
                    onClick={() => { setIsSubtitleSettingsOpen(!isSubtitleSettingsOpen); setIsSettingsOpen(false); }} 
                    className={`p-2 rounded-full transition-all duration-300 ${isSubtitleSettingsOpen ? 'bg-white/20' : 'hover:bg-white/10'} ${currentSubtitle !== 'off' ? 'text-red-400' : ''}`}
                  >
                    <Captions size={24} />
                  </button>
                  <div className={`absolute bottom-full right-0 mb-3 transition-all duration-300 ease-out ${isSubtitleSettingsOpen ? 'opacity-100 translate-y-0' : 'opacity-0 pointer-events-none'}`}>
                    <div className="bg-black/80 backdrop-blur-xl rounded-xl p-2 border border-white/10 shadow-2xl min-w-64">
                      <div className="text-xs text-white/60 px-3 py-2 font-medium">Subtitle Sync</div>
                      <div className="flex items-center justify-between px-3 py-2">
                        <span className='font-mono text-sm'>Offset: {subtitleOffset.toFixed(2)}s</span>
                        <div className="flex items-center gap-1">
                          <button onClick={() => changeSubtitleOffset(-0.25)} className="p-2 hover:bg-white/10 rounded-full"><Minus size={16} /></button>
                          <button onClick={() => changeSubtitleOffset(0.25)} className="p-2 hover:bg-white/10 rounded-full"><Plus size={16} /></button>
                        </div>
                      </div>
                      <button onClick={resyncSubtitles} className="w-full flex items-center justify-center gap-2 text-center px-3 py-2.5 text-sm rounded-lg transition-colors hover:bg-white/10 text-white/90">
                        <RefreshCw size={14}/> Auto-Resync
                      </button>
                      <div className="border-t border-white/10 my-2"></div>
                      <div className="text-xs text-white/60 px-3 py-2 font-medium">Select Language</div>
                      <div className="max-h-48 overflow-y-auto scrollbar-thin">
                        <button onClick={() => { setCurrentSubtitle('off'); setIsSubtitleSettingsOpen(false);}} className={`w-full flex justify-between items-center text-left px-3 py-2.5 text-sm rounded-lg transition-colors ${currentSubtitle === 'off' ? 'bg-red-500/20 text-red-400' : 'hover:bg-white/10 text-white/90'}`}>
                          <span>Off</span>
                          {currentSubtitle === 'off' && <Check size={16} />}
                        </button>
                        {processedSubtitles.map((subtitle) => (
                          <button key={subtitle.name} onClick={() => {setCurrentSubtitle(subtitle.name); setIsSubtitleSettingsOpen(false);}} className={`w-full flex justify-between items-center text-left px-3 py-2.5 text-sm rounded-lg transition-colors ${currentSubtitle === subtitle.name ? 'bg-red-500/20 text-red-400' : 'hover:bg-white/10 text-white/90'}`}>
                            <span>{subtitle.name}</span>
                            {currentSubtitle === subtitle.name && <Check size={16} />}
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}
              <div className="relative">
                <button onClick={() => { setIsSettingsOpen(!isSettingsOpen); setIsSubtitleSettingsOpen(false); }} className={`p-2 rounded-full transition-all duration-300 ${isSettingsOpen ? 'bg-white/20 rotate-45' : 'hover:bg-white/10'}`}>
                  <Settings size={24} />
                </button>
                <div className={`absolute bottom-full right-0 mb-3 transition-all duration-300 ease-out ${isSettingsOpen ? 'opacity-100 translate-y-0' : 'opacity-0 pointer-events-none'}`}>
                  <div className="bg-black/80 backdrop-blur-xl rounded-xl p-2 border border-white/10 shadow-2xl min-w-48">
                    <div className="text-xs text-white/60 px-3 py-2 font-medium">Quality</div>
                    {streams.map((stream) => (
                      <button key={stream.name} onClick={() => handleQualityChange(stream.url)} className={`w-full flex justify-between items-center text-left px-3 py-2.5 text-sm rounded-lg transition-colors ${currentStreamUrl === stream.url ? 'bg-red-500/20 text-red-400' : 'hover:bg-white/10 text-white/90'}`}>
                        <span>{stream.name}</span>
                        {currentStreamUrl === stream.url && <Check size={16} />}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
              <button onClick={toggleFullscreen} className="p-2 hover:bg-white/10 rounded-full transition-all duration-300 hover:scale-110">
                {isFullscreen ? <Shrink size={24} /> : <Expand size={24} />}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomVideoPlayer;