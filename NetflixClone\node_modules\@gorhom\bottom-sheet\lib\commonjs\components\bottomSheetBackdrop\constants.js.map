{"version": 3, "names": ["DEFAULT_OPACITY", "exports", "DEFAULT_APPEARS_ON_INDEX", "DEFAULT_DISAPPEARS_ON_INDEX", "DEFAULT_ENABLE_TOUCH_THROUGH", "DEFAULT_PRESS_BEHAVIOR", "DEFAULT_ACCESSIBLE", "DEFAULT_ACCESSIBILITY_ROLE", "DEFAULT_ACCESSIBILITY_LABEL", "DEFAULT_ACCESSIBILITY_HINT"], "sourceRoot": "../../../../src", "sources": ["components/bottomSheetBackdrop/constants.ts"], "mappings": ";;;;;;AAAA,MAAMA,eAAe,GAAAC,OAAA,CAAAD,eAAA,GAAG,GAAG;AAC3B,MAAME,wBAAwB,GAAAD,OAAA,CAAAC,wBAAA,GAAG,CAAC;AAClC,MAAMC,2BAA2B,GAAAF,OAAA,CAAAE,2BAAA,GAAG,CAAC;AACrC,MAAMC,4BAA4B,GAAAH,OAAA,CAAAG,4BAAA,GAAG,KAAK;AAC1C,MAAMC,sBAAsB,GAAAJ,OAAA,CAAAI,sBAAA,GAAG,OAAgB;AAE/C,MAAMC,kBAAkB,GAAAL,OAAA,CAAAK,kBAAA,GAAG,IAAI;AAC/B,MAAMC,0BAA0B,GAAAN,OAAA,CAAAM,0BAAA,GAAG,QAAQ;AAC3C,MAAMC,2BAA2B,GAAAP,OAAA,CAAAO,2BAAA,GAAG,uBAAuB;AAC3D,MAAMC,0BAA0B,GAAAR,OAAA,CAAAQ,0BAAA,GAAG,+BAA+B", "ignoreList": []}