{"version": 3, "sources": ["reducer.ts"], "names": ["registerHost", "state", "hostName", "deregisterHost", "addUpdatePortal", "portalName", "node", "index", "findIndex", "item", "name", "push", "<PERSON><PERSON><PERSON><PERSON>", "component", "reducer", "method", "params", "splice", "action", "type", "clonedState", "ACTIONS", "REGISTER_HOST", "DEREGISTER_HOST", "ADD_UPDATE_PORTAL", "REMOVE_PORTAL"], "mappings": ";;;;;;;AAAA;;AACA;;AAQA,MAAMA,YAAY,GAAG,CACnBC,KADmB,EAEnBC,QAFmB,KAGhB;AACH,MAAI,EAAEA,QAAQ,IAAID,KAAd,CAAJ,EAA0B;AACxBA,IAAAA,KAAK,CAACC,QAAD,CAAL,GAAkB,EAAlB;AACD;;AACD,SAAOD,KAAP;AACD,CARD;;AAUA,MAAME,cAAc,GAAG,CACrBF,KADqB,EAErBC,QAFqB,KAGlB;AACH,SAAOD,KAAK,CAACC,QAAD,CAAZ;AACA,SAAOD,KAAP;AACD,CAND;;AAQA,MAAMG,eAAe,GAAG,CACtBH,KADsB,EAEtBC,QAFsB,EAGtBG,UAHsB,EAItBC,IAJsB,KAKnB;AACH,MAAI,EAAEJ,QAAQ,IAAID,KAAd,CAAJ,EAA0B;AACxBA,IAAAA,KAAK,GAAGD,YAAY,CAACC,KAAD,EAAQC,QAAR,CAApB;AACD;AAED;AACF;AACA;;;AACE,QAAMK,KAAK,GAAGN,KAAK,CAACC,QAAD,CAAL,CAAgBM,SAAhB,CAA0BC,IAAI,IAAIA,IAAI,CAACC,IAAL,KAAcL,UAAhD,CAAd;;AACA,MAAIE,KAAK,KAAK,CAAC,CAAf,EAAkB;AAChBN,IAAAA,KAAK,CAACC,QAAD,CAAL,CAAgBK,KAAhB,EAAuBD,IAAvB,GAA8BA,IAA9B;AACD,GAFD,MAEO;AACLL,IAAAA,KAAK,CAACC,QAAD,CAAL,CAAgBS,IAAhB,CAAqB;AACnBD,MAAAA,IAAI,EAAEL,UADa;AAEnBC,MAAAA;AAFmB,KAArB;AAID;;AACD,SAAOL,KAAP;AACD,CAvBD;;AAyBA,MAAMW,YAAY,GAAG,CACnBX,KADmB,EAEnBC,QAFmB,EAGnBG,UAHmB,KAIhB;AACH,MAAI,EAAEH,QAAQ,IAAID,KAAd,CAAJ,EAA0B;AACxB,uBAAM;AACJY,MAAAA,SAAS,EAAEC,OAAO,CAACJ,IADf;AAEJK,MAAAA,MAAM,EAAEH,YAAY,CAACF,IAFjB;AAGJM,MAAAA,MAAM,qCAA8BX,UAA9B,iBAA+CH,QAA/C;AAHF,KAAN;AAKA,WAAOD,KAAP;AACD;;AAED,QAAMM,KAAK,GAAGN,KAAK,CAACC,QAAD,CAAL,CAAgBM,SAAhB,CAA0BC,IAAI,IAAIA,IAAI,CAACC,IAAL,KAAcL,UAAhD,CAAd;AACA,MAAIE,KAAK,KAAK,CAAC,CAAf,EAAkBN,KAAK,CAACC,QAAD,CAAL,CAAgBe,MAAhB,CAAuBV,KAAvB,EAA8B,CAA9B;AAClB,SAAON,KAAP;AACD,CAjBD;;AAmBO,MAAMa,OAAO,GAAG,CACrBb,KADqB,EAErBiB,MAFqB,KAGlB;AACH,QAAM;AAAEC,IAAAA;AAAF,MAAWD,MAAjB;AACA,MAAIE,WAAW,GAAG,EAAE,GAAGnB;AAAL,GAAlB;;AACA,UAAQkB,IAAR;AACE,SAAKE,mBAAQC,aAAb;AACE,aAAOtB,YAAY,CAACoB,WAAD,EAAcF,MAAM,CAAChB,QAArB,CAAnB;;AACF,SAAKmB,mBAAQE,eAAb;AACE,aAAOpB,cAAc,CAACiB,WAAD,EAAcF,MAAM,CAAChB,QAArB,CAArB;;AACF,SAAKmB,mBAAQG,iBAAb;AACE,aAAOpB,eAAe,CACpBgB,WADoB,EAEpBF,MAAM,CAAChB,QAFa,EAGnBgB,MAAD,CAAkCb,UAHd,EAInBa,MAAD,CAAkCZ,IAJd,CAAtB;;AAMF,SAAKe,mBAAQI,aAAb;AACE,aAAOb,YAAY,CACjBQ,WADiB,EAEjBF,MAAM,CAAChB,QAFU,EAGhBgB,MAAD,CAA+Bb,UAHd,CAAnB;;AAKF;AACE,aAAOJ,KAAP;AAnBJ;AAqBD,CA3BM", "sourcesContent": ["import { ACTIONS } from './constants';\nimport { print } from '../utilities/logger';\nimport type { PortalType } from '../types';\nimport type {\n  ActionTypes,\n  AddUpdatePortalAction,\n  RemovePortalAction,\n} from './types';\n\nconst registerHost = (\n  state: Record<string, Array<PortalType>>,\n  hostName: string\n) => {\n  if (!(hostName in state)) {\n    state[hostName] = [];\n  }\n  return state;\n};\n\nconst deregisterHost = (\n  state: Record<string, Array<PortalType>>,\n  hostName: string\n) => {\n  delete state[hostName];\n  return state;\n};\n\nconst addUpdatePortal = (\n  state: Record<string, Array<PortalType>>,\n  hostName: string,\n  portalName: string,\n  node: any\n) => {\n  if (!(hostName in state)) {\n    state = registerHost(state, hostName);\n  }\n\n  /**\n   * updated portal, if it was already added.\n   */\n  const index = state[hostName].findIndex(item => item.name === portalName);\n  if (index !== -1) {\n    state[hostName][index].node = node;\n  } else {\n    state[hostName].push({\n      name: portalName,\n      node,\n    });\n  }\n  return state;\n};\n\nconst removePortal = (\n  state: Record<string, Array<PortalType>>,\n  hostName: string,\n  portalName: string\n) => {\n  if (!(hostName in state)) {\n    print({\n      component: reducer.name,\n      method: removePortal.name,\n      params: `Failed to remove portal '${portalName}', '${hostName}' was not registered!`,\n    });\n    return state;\n  }\n\n  const index = state[hostName].findIndex(item => item.name === portalName);\n  if (index !== -1) state[hostName].splice(index, 1);\n  return state;\n};\n\nexport const reducer = (\n  state: Record<string, Array<PortalType>>,\n  action: ActionTypes\n) => {\n  const { type } = action;\n  let clonedState = { ...state };\n  switch (type) {\n    case ACTIONS.REGISTER_HOST:\n      return registerHost(clonedState, action.hostName);\n    case ACTIONS.DEREGISTER_HOST:\n      return deregisterHost(clonedState, action.hostName);\n    case ACTIONS.ADD_UPDATE_PORTAL:\n      return addUpdatePortal(\n        clonedState,\n        action.hostName,\n        (action as AddUpdatePortalAction).portalName,\n        (action as AddUpdatePortalAction).node\n      );\n    case ACTIONS.REMOVE_PORTAL:\n      return removePortal(\n        clonedState,\n        action.hostName,\n        (action as RemovePortalAction).portalName\n      );\n    default:\n      return state;\n  }\n};\n"]}