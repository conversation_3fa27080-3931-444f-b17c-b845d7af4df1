# React Native Bottom Sheet

[![Reanimated v3 version](https://img.shields.io/github/package-json/v/gorhom/react-native-bottom-sheet/master?label=Reanimated%20v3&style=flat-square)](https://www.npmjs.com/package/@gorhom/bottom-sheet) [![Reanimated v2 version](https://img.shields.io/github/package-json/v/gorhom/react-native-bottom-sheet/v4?label=Reanimated%20v2&style=flat-square)](https://www.npmjs.com/package/@gorhom/bottom-sheet)  [![Reanimated v1 version](https://img.shields.io/github/package-json/v/gorhom/react-native-bottom-sheet/v2?label=Reanimated%20v1&style=flat-square)](https://www.npmjs.com/package/@gorhom/bottom-sheet)<br>
[![license](https://img.shields.io/npm/l/@gorhom/bottom-sheet?style=flat-square)](https://www.npmjs.com/package/@gorhom/bottom-sheet) [![npm](https://img.shields.io/badge/types-included-blue?style=flat-square)](https://www.npmjs.com/package/@gorhom/bottom-sheet) [![runs with expo](https://img.shields.io/badge/Runs%20with%20Expo-4630EB.svg?style=flat-square&logo=EXPO&labelColor=f3f3f3&logoColor=000)](https://expo.io/) <br> ![NPM Downloads](https://img.shields.io/npm/dw/%40gorhom%2Fbottom-sheet?style=flat-square)


A performant interactive bottom sheet with fully configurable options 🚀

![React Native Bottom Sheet](./preview.gif)

---

## Features
- ⭐️ Support React Native Web, [read more](https://gorhom.dev/react-native-bottom-sheet/web-support).
- ⭐️ Dynamic Sizing, [read more](https://gorhom.dev/react-native-bottom-sheet/dynamic-sizing).
- ⭐️ Support FlashList, [read more](https://gorhom.dev/react-native-bottom-sheet/components/bottomsheetflashlist).
- Modal presentation view, [Bottom Sheet Modal](https://gorhom.dev/react-native-bottom-sheet/modal).
- Smooth gesture interactions & snapping animations.
- Seamless [keyboard handling](https://gorhom.dev/react-native-bottom-sheet/keyboard-handling) for iOS & Android.
- Support [pull to refresh](https://gorhom.dev/react-native-bottom-sheet/pull-to-refresh) for scrollables.
- Support `FlatList`, `SectionList`, `ScrollView` & `View` scrolling interactions, [read more](https://gorhom.dev/react-native-bottom-sheet/scrollables).
- Support `React Navigation` Integration, [read more](https://gorhom.dev/react-native-bottom-sheet/react-navigation-integration).
- Compatible with `Reanimated` v1-3.
- Compatible with `Expo`.
- Accessibility support.
- Written in `TypeScript`.
- [Read more](https://gorhom.dev/react-native-bottom-sheet).

## Getting Started

Check out [the documentation website](https://gorhom.dev/react-native-bottom-sheet).

## Versioning

This library been written in 3 versions of `Reanimated`, and kept all implementation in separate branches:

- **`v5`** | [branch](https://github.com/gorhom/react-native-bottom-sheet/tree/master) | [changelog](https://github.com/gorhom/react-native-bottom-sheet/blob/master/CHANGELOG.md) : written with `Reanimated v3` & `Gesture Handler v2`.

- `v4` (not maintained) | [branch](https://github.com/gorhom/react-native-bottom-sheet/tree/v4) | [changelog](https://github.com/gorhom/react-native-bottom-sheet/blob/v4/CHANGELOG.md) : written with `Reanimated v2`.

- `v2` (not maintained) | [branch](https://github.com/gorhom/react-native-bottom-sheet/tree/v2) | [changelog](https://github.com/gorhom/react-native-bottom-sheet/blob/v2/CHANGELOG.md) : written with `Reanimated v1` & compatible with `Reanimated v2`.

> I highly recommend to use `v5` which provides more stability with all latest features.

## Author

- [Mo Gorhom](https://gorhom.dev/)

## Sponsor & Support

To keep this library maintained and up-to-date please consider [sponsoring it on GitHub](https://github.com/sponsors/gorhom). Or if you are looking for a private support or help in customizing the experience, then reach out to me on Twitter [@gorhom](https://twitter.com/gorhom).

## License

[MIT](./LICENSE)

---

<p align="center">
  <a href="https://gorhom.dev/#gh-light-mode-only" target="_blank">
    <img height="18" alt="Mo Gorhom" src="./mogorhom-light.png">
  </a>
  <a href="https://gorhom.dev/#gh-dark-mode-only" target="_blank">
    <img height="18" alt="Mo Gorhom" src="./mogorhom-dark.png">
  </a>
</p>
