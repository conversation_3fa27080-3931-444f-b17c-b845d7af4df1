import React from 'react';
import type { BottomSheetHandleContainerProps } from './types';
declare function BottomSheetHandleContainerComponent({ animatedIndex, animatedPosition, simultaneousHandlers: _internalSimultaneousHandlers, enableHandlePanningGesture, handleHeight, handleComponent, handleStyle: _providedHandleStyle, handleIndicatorStyle: _providedIndicatorStyle, }: BottomSheetHandleContainerProps): React.JSX.Element;
declare const BottomSheetHandleContainer: React.MemoExoticComponent<typeof BottomSheetHandleContainerComponent>;
export default BottomSheetHandleContainer;
//# sourceMappingURL=BottomSheetHandleContainer.d.ts.map