{"version": 3, "sources": ["portal.ts"], "names": ["PortalStateContext", "PortalDispatchContext"], "mappings": ";;;;;;;AAAA;;AAIO,MAAMA,kBAAkB,gBAAG,0BAGxB,IAHwB,CAA3B;;AAIA,MAAMC,qBAAqB,gBAChC,0BAAkD,IAAlD,CADK", "sourcesContent": ["import { createContext } from 'react';\nimport type { ActionTypes } from '../state/types';\nimport type { PortalType } from '../types';\n\nexport const PortalStateContext = createContext<Record<\n  string,\n  Array<PortalType>\n> | null>(null);\nexport const PortalDispatchContext =\n  createContext<React.Dispatch<ActionTypes> | null>(null);\n"]}