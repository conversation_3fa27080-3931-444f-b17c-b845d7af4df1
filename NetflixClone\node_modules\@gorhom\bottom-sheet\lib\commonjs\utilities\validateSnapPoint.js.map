{"version": 3, "names": ["_invariant", "_interopRequireDefault", "require", "e", "__esModule", "default", "validateSnapPoint", "snapPoint", "invariant", "includes", "Number", "split", "exports"], "sourceRoot": "../../../src", "sources": ["utilities/validateSnapPoint.ts"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAkC,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE3B,MAAMG,iBAAiB,GAAIC,SAA0B,IAAK;EAC/D,IAAAC,kBAAS,EACP,OAAOD,SAAS,KAAK,QAAQ,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAC9D,IAAIA,SAAS,mEACf,CAAC;EAED,IAAAC,kBAAS,EACP,OAAOD,SAAS,KAAK,QAAQ,IAC1B,OAAOA,SAAS,KAAK,QAAQ,IAAIA,SAAS,CAACE,QAAQ,CAAC,GAAG,CAAE,EAC5D,IAAIF,SAAS,qGACf,CAAC;EAED,IAAAC,kBAAS,EACP,OAAOD,SAAS,KAAK,QAAQ,IAC1B,OAAOA,SAAS,KAAK,QAAQ,IAAIG,MAAM,CAACH,SAAS,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAE,EACpE,IAAIJ,SAAS,iHACf,CAAC;AACH,CAAC;AAACK,OAAA,CAAAN,iBAAA,GAAAA,iBAAA", "ignoreList": []}