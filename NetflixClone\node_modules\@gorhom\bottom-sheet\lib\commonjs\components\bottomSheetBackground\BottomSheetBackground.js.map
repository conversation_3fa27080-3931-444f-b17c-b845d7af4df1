{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_reactNative", "_styles", "_jsxRuntime", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "BottomSheetBackgroundComponent", "pointerEvents", "style", "jsx", "View", "accessible", "accessibilityRole", "accessibilityLabel", "styles", "background", "BottomSheetBackground", "exports", "memo", "displayName"], "sourceRoot": "../../../../src", "sources": ["components/bottomSheetBackground/BottomSheetBackground.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AAAkC,IAAAG,WAAA,GAAAH,OAAA;AAAA,SAAAI,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAN,wBAAAM,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAGlC,MAAMW,8BAA8B,GAAGA,CAAC;EACtCC,aAAa;EACbC;AAC0B,CAAC,kBAC3B,IAAAvB,WAAA,CAAAwB,GAAA,EAAC1B,YAAA,CAAA2B,IAAI;EACHH,aAAa,EAAEA,aAAc;EAC7BI,UAAU,EAAE,IAAK;EACjBC,iBAAiB,EAAC,YAAY;EAC9BC,kBAAkB,EAAC,cAAc;EACjCL,KAAK,EAAE,CAACM,cAAM,CAACC,UAAU,EAAEP,KAAK;AAAE,CACnC,CACF;AAEM,MAAMQ,qBAAqB,GAAAC,OAAA,CAAAD,qBAAA,gBAAG,IAAAE,WAAI,EAACZ,8BAA8B,CAAC;AACzEU,qBAAqB,CAACG,WAAW,GAAG,uBAAuB", "ignoreList": []}