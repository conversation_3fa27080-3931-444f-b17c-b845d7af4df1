{"version": 3, "sources": ["usePortalState.ts"], "names": ["useContext", "PortalStateContext", "usePortalState", "hostName", "state", "Error"], "mappings": "AAAA,SAASA,UAAT,QAA2B,OAA3B;AACA,SAASC,kBAAT,QAAmC,oBAAnC;AAEA,OAAO,MAAMC,cAAc,GAAIC,QAAD,IAAsB;AAClD,QAAMC,KAAK,GAAGJ,UAAU,CAACC,kBAAD,CAAxB;;AAEA,MAAIG,KAAK,KAAK,IAAd,EAAoB;AAClB,UAAM,IAAIC,KAAJ,CACJ,yFADI,CAAN;AAGD;;AAED,SAAOD,KAAK,CAACD,QAAD,CAAL,IAAmB,EAA1B;AACD,CAVM", "sourcesContent": ["import { useContext } from 'react';\nimport { PortalStateContext } from '../contexts/portal';\n\nexport const usePortalState = (hostName: string) => {\n  const state = useContext(PortalStateContext);\n\n  if (state === null) {\n    throw new Error(\n      \"'PortalStateContext' cannot be null, please add 'PortalProvider' to the root component.\"\n    );\n  }\n\n  return state[hostName] || [];\n};\n"]}