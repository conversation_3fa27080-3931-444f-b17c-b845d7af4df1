{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_reactNativeGestureHandler", "_reactNativeReanimated", "_interopRequireDefault", "_gesture", "_hooks", "_jsxRuntime", "e", "__esModule", "default", "_getRequireWildcardCache", "WeakMap", "r", "t", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "BottomSheetDraggableViewComponent", "nativeGestureRef", "refreshControlGestureRef", "style", "children", "rest", "enableContentPanningGesture", "simultaneousHandlers", "_providedSimultaneousHandlers", "waitFor", "activeOffsetX", "activeOffsetY", "failOffsetX", "failOffsetY", "useBottomSheetInternal", "contentPanGestureHandler", "useBottomSheetGestureHandlers", "useMemo", "refs", "push", "Array", "isArray", "draggableGesture", "gesture", "Gesture", "Pan", "enabled", "shouldCancelWhenOutside", "runOnJS", "onStart", "handleOnStart", "onChange", "handleOnChange", "onEnd", "handleOnEnd", "onFinalize", "handleOnFinalize", "requireExternalGestureToFail", "simultaneousWithExternalGesture", "jsx", "GestureDetector", "BottomSheetDraggableContext", "Provider", "value", "View", "BottomSheetDraggableView", "memo", "displayName", "_default", "exports"], "sourceRoot": "../../../../src", "sources": ["components/bottomSheetDraggableView/BottomSheetDraggableView.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,0BAAA,GAAAD,OAAA;AACA,IAAAE,sBAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,QAAA,GAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AAGqB,IAAAM,WAAA,GAAAN,OAAA;AAAA,SAAAG,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,yBAAAH,CAAA,6BAAAI,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAD,wBAAA,YAAAA,CAAAH,CAAA,WAAAA,CAAA,GAAAM,CAAA,GAAAD,CAAA,KAAAL,CAAA;AAAA,SAAAR,wBAAAQ,CAAA,EAAAK,CAAA,SAAAA,CAAA,IAAAL,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAE,OAAA,EAAAF,CAAA,QAAAM,CAAA,GAAAH,wBAAA,CAAAE,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAP,CAAA,UAAAM,CAAA,CAAAE,GAAA,CAAAR,CAAA,OAAAS,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAf,CAAA,oBAAAe,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAe,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAd,CAAA,EAAAe,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAf,CAAA,CAAAe,CAAA,YAAAN,CAAA,CAAAP,OAAA,GAAAF,CAAA,EAAAM,CAAA,IAAAA,CAAA,CAAAa,GAAA,CAAAnB,CAAA,EAAAS,CAAA,GAAAA,CAAA;AAGrB,MAAMW,iCAAiC,GAAGA,CAAC;EACzCC,gBAAgB;EAChBC,wBAAwB;EACxBC,KAAK;EACLC,QAAQ;EACR,GAAGC;AAC0B,CAAC,KAAK;EACnC;EACA,MAAM;IACJC,2BAA2B;IAC3BC,oBAAoB,EAAEC,6BAA6B;IACnDC,OAAO;IACPC,aAAa;IACbC,aAAa;IACbC,WAAW;IACXC;EACF,CAAC,GAAG,IAAAC,6BAAsB,EAAC,CAAC;EAC5B,MAAM;IAAEC;EAAyB,CAAC,GAAG,IAAAC,oCAA6B,EAAC,CAAC;EACpE;;EAEA;EACA,MAAMT,oBAAoB,GAAG,IAAAU,cAAO,EAAC,MAAM;IACzC,MAAMC,IAAI,GAAG,EAAE;IAEf,IAAIjB,gBAAgB,EAAE;MACpBiB,IAAI,CAACC,IAAI,CAAClB,gBAAgB,CAAC;IAC7B;IAEA,IAAIC,wBAAwB,EAAE;MAC5BgB,IAAI,CAACC,IAAI,CAACjB,wBAAwB,CAAC;IACrC;IAEA,IAAIM,6BAA6B,EAAE;MACjC,IAAIY,KAAK,CAACC,OAAO,CAACb,6BAA6B,CAAC,EAAE;QAChDU,IAAI,CAACC,IAAI,CAAC,GAAGX,6BAA6B,CAAC;MAC7C,CAAC,MAAM;QACLU,IAAI,CAACC,IAAI,CAACX,6BAA6B,CAAC;MAC1C;IACF;IAEA,OAAOU,IAAI;EACb,CAAC,EAAE,CACDV,6BAA6B,EAC7BP,gBAAgB,EAChBC,wBAAwB,CACzB,CAAC;EACF,MAAMoB,gBAAgB,GAAG,IAAAL,cAAO,EAAC,MAAM;IACrC,IAAIM,OAAO,GAAGC,kCAAO,CAACC,GAAG,CAAC,CAAC,CACxBC,OAAO,CAACpB,2BAA2B,CAAC,CACpCqB,uBAAuB,CAAC,KAAK,CAAC,CAC9BC,OAAO,CAAC,KAAK,CAAC,CACdC,OAAO,CAACd,wBAAwB,CAACe,aAAa,CAAC,CAC/CC,QAAQ,CAAChB,wBAAwB,CAACiB,cAAc,CAAC,CACjDC,KAAK,CAAClB,wBAAwB,CAACmB,WAAW,CAAC,CAC3CC,UAAU,CAACpB,wBAAwB,CAACqB,gBAAgB,CAAC;IAExD,IAAI3B,OAAO,EAAE;MACXc,OAAO,GAAGA,OAAO,CAACc,4BAA4B,CAAC5B,OAAO,CAAC;IACzD;IAEA,IAAIF,oBAAoB,EAAE;MACxBgB,OAAO,GAAGA,OAAO,CAACe,+BAA+B,CAC/C/B,oBACF,CAAC;IACH;IAEA,IAAIG,aAAa,EAAE;MACjBa,OAAO,GAAGA,OAAO,CAACb,aAAa,CAACA,aAAa,CAAC;IAChD;IAEA,IAAIC,aAAa,EAAE;MACjBY,OAAO,GAAGA,OAAO,CAACZ,aAAa,CAACA,aAAa,CAAC;IAChD;IAEA,IAAIC,WAAW,EAAE;MACfW,OAAO,GAAGA,OAAO,CAACX,WAAW,CAACA,WAAW,CAAC;IAC5C;IAEA,IAAIC,WAAW,EAAE;MACfU,OAAO,GAAGA,OAAO,CAACV,WAAW,CAACA,WAAW,CAAC;IAC5C;IAEA,OAAOU,OAAO;EAChB,CAAC,EAAE,CACDb,aAAa,EACbC,aAAa,EACbL,2BAA2B,EAC3BM,WAAW,EACXC,WAAW,EACXN,oBAAoB,EACpBE,OAAO,EACPM,wBAAwB,CAACiB,cAAc,EACvCjB,wBAAwB,CAACmB,WAAW,EACpCnB,wBAAwB,CAACqB,gBAAgB,EACzCrB,wBAAwB,CAACe,aAAa,CACvC,CAAC;EACF;;EAEA,oBACE,IAAAnD,WAAA,CAAA4D,GAAA,EAACjE,0BAAA,CAAAkE,eAAe;IAACjB,OAAO,EAAED,gBAAiB;IAAAlB,QAAA,eACzC,IAAAzB,WAAA,CAAA4D,GAAA,EAAC9D,QAAA,CAAAgE,2BAA2B,CAACC,QAAQ;MAACC,KAAK,EAAErB,gBAAiB;MAAAlB,QAAA,eAC5D,IAAAzB,WAAA,CAAA4D,GAAA,EAAChE,sBAAA,CAAAO,OAAQ,CAAC8D,IAAI;QAACzC,KAAK,EAAEA,KAAM;QAAA,GAAKE,IAAI;QAAAD,QAAA,EAClCA;MAAQ,CACI;IAAC,CACoB;EAAC,CACxB,CAAC;AAEtB,CAAC;AAED,MAAMyC,wBAAwB,gBAAG,IAAAC,WAAI,EAAC9C,iCAAiC,CAAC;AACxE6C,wBAAwB,CAACE,WAAW,GAAG,0BAA0B;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAnE,OAAA,GAEnD+D,wBAAwB", "ignoreList": []}