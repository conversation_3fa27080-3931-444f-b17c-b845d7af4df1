{"version": 3, "sources": ["PortalHost.tsx"], "names": ["React", "memo", "useEffect", "usePortalState", "usePortal", "PortalHostComponent", "name", "state", "registerHost", "deregisterHost", "map", "item", "node", "PortalHost", "displayName"], "mappings": "AAAA,OAAOA,KAAP,IAAgBC,IAAhB,EAAsBC,SAAtB,QAAuC,OAAvC;AACA,SAASC,cAAT,QAA+B,4BAA/B;AACA,SAASC,SAAT,QAA0B,uBAA1B;;AAGA,MAAMC,mBAAmB,GAAG,CAAC;AAAEC,EAAAA;AAAF,CAAD,KAA+B;AACzD;AACA,QAAMC,KAAK,GAAGJ,cAAc,CAACG,IAAD,CAA5B;AACA,QAAM;AAAEE,IAAAA,YAAF;AAAgBC,IAAAA;AAAhB,MAAmCL,SAAS,CAACE,IAAD,CAAlD,CAHyD,CAIzD;AAEA;;AACAJ,EAAAA,SAAS,CAAC,MAAM;AACdM,IAAAA,YAAY;AACZ,WAAO,MAAM;AACXC,MAAAA,cAAc;AACf,KAFD,CAFc,CAKd;AACD,GANQ,EAMN,EANM,CAAT,CAPyD,CAczD;AAEA;;AACA,sBAAO,0CAAGF,KAAK,CAACG,GAAN,CAAUC,IAAI,IAAIA,IAAI,CAACC,IAAvB,CAAH,CAAP,CAjByD,CAkBzD;AACD,CAnBD;;AAqBA,OAAO,MAAMC,UAAU,gBAAGZ,IAAI,CAACI,mBAAD,CAAvB;AACPQ,UAAU,CAACC,WAAX,GAAyB,YAAzB", "sourcesContent": ["import React, { memo, useEffect } from 'react';\nimport { usePortalState } from '../../hooks/usePortalState';\nimport { usePortal } from '../../hooks/usePortal';\nimport type { PortalHostProps } from './types';\n\nconst PortalHostComponent = ({ name }: PortalHostProps) => {\n  //#region hooks\n  const state = usePortalState(name);\n  const { registerHost, deregisterHost } = usePortal(name);\n  //#endregion\n\n  //#region effects\n  useEffect(() => {\n    registerHost();\n    return () => {\n      deregisterHost();\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  //#endregion\n\n  //#region render\n  return <>{state.map(item => item.node)}</>;\n  //#endregion\n};\n\nexport const PortalHost = memo(PortalHostComponent);\nPortalHost.displayName = 'PortalHost';\n"]}