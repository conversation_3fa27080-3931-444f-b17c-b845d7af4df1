"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.PortalProvider = void 0;

var _react = _interopRequireWildcard(require("react"));

var _PortalHost = require("../portalHost/PortalHost");

var _portal = require("../../contexts/portal");

var _constants = require("../../state/constants");

var _reducer = require("../../state/reducer");

function _getRequireWildcardCache() { if (typeof WeakMap !== "function") return null; var cache = new WeakMap(); _getRequireWildcardCache = function () { return cache; }; return cache; }

function _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }

const PortalProviderComponent = ({
  rootHostName = 'root',
  shouldAddRootHost = true,
  children
}) => {
  const [state, dispatch] = (0, _react.useReducer)(_reducer.reducer, _constants.INITIAL_STATE);
  return /*#__PURE__*/_react.default.createElement(_portal.PortalDispatchContext.Provider, {
    value: dispatch
  }, /*#__PURE__*/_react.default.createElement(_portal.PortalStateContext.Provider, {
    value: state
  }, children, shouldAddRootHost && /*#__PURE__*/_react.default.createElement(_PortalHost.PortalHost, {
    name: rootHostName
  })));
};

const PortalProvider = /*#__PURE__*/(0, _react.memo)(PortalProviderComponent);
exports.PortalProvider = PortalProvider;
PortalProvider.displayName = 'PortalProvider';
//# sourceMappingURL=PortalProvider.js.map