{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_BottomSheetDraggableScrollable", "_jsxRuntime", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "ScrollableContainer", "exports", "forwardRef", "nativeGesture", "ScrollableComponent", "rest", "ref", "jsx", "BottomSheetDraggableScrollable", "scrollableGesture", "children"], "sourceRoot": "../../../../src", "sources": ["components/bottomSheetScrollable/ScrollableContainer.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,+BAAA,GAAAD,OAAA;AAAkF,IAAAE,WAAA,GAAAF,OAAA;AAAA,SAAAG,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAL,wBAAAK,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAQ3E,MAAMW,mBAAmB,GAAAC,OAAA,CAAAD,mBAAA,gBAAG,IAAAE,iBAAU,EAC3C,SAASF,mBAAmBA,CAC1B;EAAEG,aAAa;EAAEC,mBAAmB;EAAE,GAAGC;AAAK,CAAC,EAC/CC,GAAG,EACH;EACA,oBACE,IAAA3B,WAAA,CAAA4B,GAAA,EAAC7B,+BAAA,CAAA8B,8BAA8B;IAACC,iBAAiB,EAAEN,aAAc;IAAAO,QAAA,eAC/D,IAAA/B,WAAA,CAAA4B,GAAA,EAACH,mBAAmB;MAACE,GAAG,EAAEA,GAAI;MAAA,GAAKD;IAAI,CAAG;EAAC,CACb,CAAC;AAErC,CACF,CAAC", "ignoreList": []}