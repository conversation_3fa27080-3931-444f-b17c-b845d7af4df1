{"version": 3, "names": ["_reactNative", "require", "styles", "exports", "StyleSheet", "create", "container", "position", "top", "left", "right", "zIndex", "pointerEvents"], "sourceRoot": "../../../../src", "sources": ["components/bottomSheetFooter/styles.ts"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEO,MAAMC,MAAM,GAAAC,OAAA,CAAAD,MAAA,GAAGE,uBAAU,CAACC,MAAM,CAAC;EACtCC,SAAS,EAAE;IACTC,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,IAAI;IACZC,aAAa,EAAE;EACjB;AACF,CAAC,CAAC", "ignoreList": []}