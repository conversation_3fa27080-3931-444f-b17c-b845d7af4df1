{"name": "@gorhom/portal", "version": "1.0.14", "description": "A simplified portal implementation for ⭕️ React Native ⭕️", "main": "lib/commonjs/index", "module": "lib/module/index", "types": "lib/typescript/index.d.ts", "react-native": "src/index", "source": "src/index", "files": ["src", "lib"], "keywords": ["expo", "react-native-web", "react-native", "ios", "android", "portal"], "repository": "https://github.com/gorhom/react-native-portal", "author": "<PERSON> (https://gorhom.dev)", "license": "MIT", "bugs": {"url": "https://github.com/gorhom/react-native-portal/issues"}, "homepage": "https://github.com/gorhom/react-native-portal#readme", "scripts": {"test": "jest", "typescript": "tsc --noEmit", "lint": "eslint \"**/*.{js,ts,tsx}\"", "build": "yarn delete-build && bob build && yarn copy-dts && yarn delete-dts.js", "release": "release-it", "copy-dts": "copyfiles -u 1 \"src/**/*.d.ts\" lib/typescript", "delete-build": "rm -rf lib", "delete-dts.js": "find ./lib/commonjs -name '*.d.js*' -delete && find ./lib/module -name '*.d.js*' -delete", "example": "yarn --cwd example", "bootstrap": "yarn example && yarn && pod install"}, "dependencies": {"nanoid": "^3.3.1"}, "devDependencies": {"@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@react-native-community/bob": "^0.17.1", "@react-native-community/eslint-config": "^2.0.0", "@release-it/conventional-changelog": "^2.0.0", "@types/jest": "^26.0.0", "@types/lodash.isequal": "^4.5.5", "@types/react": "^17.0.0", "@types/react-native": "0.63.37", "auto-changelog": "^2.2.1", "copyfiles": "^2.4.1", "eslint": "^7.28.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^3.4.0", "husky": "^4.3.8", "jest": "^26.6.3", "prettier": "^2.3.1", "react": "16.11.0", "react-native": "0.62.2", "release-it": "^14.2.2", "typescript": "^4.3.2"}, "peerDependencies": {"react": "*", "react-native": "*"}, "@react-native-community/bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", "typescript"]}}