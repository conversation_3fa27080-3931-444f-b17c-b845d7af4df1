{"version": 3, "names": ["useDerivedValue", "useSharedValue", "INITIAL_CONTAINER_HEIGHT", "INITIAL_HANDLE_HEIGHT", "INITIAL_SNAP_POINT", "normalizeSnapPoint", "useAnimatedSnapPoints", "snapPoints", "containerHeight", "contentHeight", "handleHeight", "footerHeight", "enableDynamicSizing", "maxDynamicContentSize", "dynamicSnapPointIndex", "normalizedSnapPoints", "isContainerLayoutReady", "value", "_snapPoints", "_normalizedSnapPoints", "map", "snapPoint", "dynamicSnapPoint", "Math", "min", "undefined", "includes", "push", "sort", "a", "b", "indexOf", "hasDynamicSnapPoint", "length", "find"], "sourceRoot": "../../../src", "sources": ["hooks/useAnimatedSnapPoints.ts"], "mappings": ";;AAAA,SAEEA,eAAe,EACfC,cAAc,QACT,yBAAyB;AAEhC,SACEC,wBAAwB,EACxBC,qBAAqB,EACrBC,kBAAkB,QACb,qCAAqC;AAC5C,SAASC,kBAAkB,QAAQ,cAAc;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,qBAAqB,GAAGA,CACnCC,UAA0C,EAC1CC,eAAoC,EACpCC,aAAkC,EAClCC,YAAiC,EACjCC,YAAiC,EACjCC,mBAA4D,EAC5DC,qBAAgE,KACO;EACvE,MAAMC,qBAAqB,GAAGb,cAAc,CAAS,CAAC,CAAC,CAAC;EACxD,MAAMc,oBAAoB,GAAGf,eAAe,CAAC,MAAM;IACjD;IACA,MAAMgB,sBAAsB,GAC1BR,eAAe,CAACS,KAAK,KAAKf,wBAAwB;IACpD,IAAI,CAACc,sBAAsB,EAAE;MAC3B,OAAO,CAACZ,kBAAkB,CAAC;IAC7B;;IAEA;IACA,MAAMc,WAAW,GAAGX,UAAU,GAC1B,OAAO,IAAIA,UAAU,GACnBA,UAAU,CAACU,KAAK,GAChBV,UAAU,GACZ,EAAE;;IAEN;IACA;IACA,IAAIY,qBAAqB,GAAGD,WAAW,CAACE,GAAG,CAACC,SAAS,IACnDhB,kBAAkB,CAACgB,SAAS,EAAEb,eAAe,CAACS,KAAK,CACrD,CAAa;;IAEb;IACA,IAAI,CAACL,mBAAmB,EAAE;MACxB,OAAOO,qBAAqB;IAC9B;;IAEA;IACA,IAAIT,YAAY,CAACO,KAAK,KAAKd,qBAAqB,EAAE;MAChD,OAAO,CAACC,kBAAkB,CAAC;IAC7B;;IAEA;IACA,IAAIK,aAAa,CAACQ,KAAK,KAAKf,wBAAwB,EAAE;MACpD,OAAO,CAACE,kBAAkB,CAAC;IAC7B;;IAEA;IACA,MAAMkB,gBAAgB,GACpBd,eAAe,CAACS,KAAK,GACrBM,IAAI,CAACC,GAAG,CACNf,aAAa,CAACQ,KAAK,GAAGP,YAAY,CAACO,KAAK,EACxCJ,qBAAqB,KAAKY,SAAS,GAC/BZ,qBAAqB,GACrBL,eAAe,CAACS,KACtB,CAAC;;IAEH;IACA;IACA,IAAI,CAACE,qBAAqB,CAACO,QAAQ,CAACJ,gBAAgB,CAAC,EAAE;MACrDH,qBAAqB,CAACQ,IAAI,CAACL,gBAAgB,CAAC;IAC9C;;IAEA;IACAH,qBAAqB,GAAGA,qBAAqB,CAACS,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC;;IAEnE;IACAf,qBAAqB,CAACG,KAAK,GACzBE,qBAAqB,CAACY,OAAO,CAACT,gBAAgB,CAAC;IAEjD,OAAOH,qBAAqB;EAC9B,CAAC,EAAE,CACDZ,UAAU,EACVC,eAAe,EACfE,YAAY,EACZD,aAAa,EACbE,YAAY,EACZC,mBAAmB,EACnBC,qBAAqB,EACrBC,qBAAqB,CACtB,CAAC;EAEF,MAAMkB,mBAAmB,GAAGhC,eAAe,CAAC,MAAM;IAChD;AACJ;AACA;IACI,IAAIY,mBAAmB,EAAE;MACvB,OAAO,IAAI;IACb;;IAEA;IACA,MAAMM,WAAW,GAAGX,UAAU,GAC1B,OAAO,IAAIA,UAAU,GACnBA,UAAU,CAACU,KAAK,GAChBV,UAAU,GACZ,EAAE;;IAEN;AACJ;AACA;IACI,IACEW,WAAW,CAACe,MAAM,IAClBf,WAAW,CAACgB,IAAI,CAACb,SAAS,IAAI,OAAOA,SAAS,KAAK,QAAQ,CAAC,EAC5D;MACA,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd,CAAC,CAAC;EAEF,OAAO,CAACN,oBAAoB,EAAED,qBAAqB,EAAEkB,mBAAmB,CAAC;AAC3E,CAAC", "ignoreList": []}