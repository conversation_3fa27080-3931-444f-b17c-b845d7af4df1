import type { WithSpringConfig } from 'react-native-reanimated';
/**
 * Generate spring animation configs.
 * @param configs overridable configs.
 */
export declare const useBottomSheetSpringConfigs: (configs: Omit<WithSpringConfig, "velocity">) => Omit<import("react-native-reanimated/lib/typescript/animation/springUtils").SpringConfig, "velocity">;
//# sourceMappingURL=useBottomSheetSpringConfigs.d.ts.map