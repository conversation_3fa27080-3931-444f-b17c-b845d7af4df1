{"version": 3, "sources": ["PortalProvider.tsx"], "names": ["React", "memo", "useReducer", "PortalHost", "PortalDispatchContext", "PortalStateContext", "INITIAL_STATE", "reducer", "PortalProviderComponent", "rootHostName", "shouldAddRootHost", "children", "state", "dispatch", "PortalProvider", "displayName"], "mappings": "AAAA,OAAOA,KAAP,IAAgBC,IAAhB,EAAsBC,UAAtB,QAAwC,OAAxC;AACA,SAASC,UAAT,QAA2B,0BAA3B;AACA,SACEC,qBADF,EAEEC,kBAFF,QAGO,uBAHP;AAIA,SAASC,aAAT,QAA8B,uBAA9B;AACA,SAASC,OAAT,QAAwB,qBAAxB;;AAGA,MAAMC,uBAAuB,GAAG,CAAC;AAC/BC,EAAAA,YAAY,GAAG,MADgB;AAE/BC,EAAAA,iBAAiB,GAAG,IAFW;AAG/BC,EAAAA;AAH+B,CAAD,KAIL;AACzB,QAAM,CAACC,KAAD,EAAQC,QAAR,IAAoBX,UAAU,CAACK,OAAD,EAAUD,aAAV,CAApC;AACA,sBACE,oBAAC,qBAAD,CAAuB,QAAvB;AAAgC,IAAA,KAAK,EAAEO;AAAvC,kBACE,oBAAC,kBAAD,CAAoB,QAApB;AAA6B,IAAA,KAAK,EAAED;AAApC,KACGD,QADH,EAEGD,iBAAiB,iBAAI,oBAAC,UAAD;AAAY,IAAA,IAAI,EAAED;AAAlB,IAFxB,CADF,CADF;AAQD,CAdD;;AAgBA,OAAO,MAAMK,cAAc,gBAAGb,IAAI,CAACO,uBAAD,CAA3B;AACPM,cAAc,CAACC,WAAf,GAA6B,gBAA7B", "sourcesContent": ["import React, { memo, useReducer } from 'react';\nimport { PortalHost } from '../portalHost/PortalHost';\nimport {\n  PortalDispatchContext,\n  PortalStateContext,\n} from '../../contexts/portal';\nimport { INITIAL_STATE } from '../../state/constants';\nimport { reducer } from '../../state/reducer';\nimport type { PortalProviderProps } from './types';\n\nconst PortalProviderComponent = ({\n  rootHostName = 'root',\n  shouldAddRootHost = true,\n  children,\n}: PortalProviderProps) => {\n  const [state, dispatch] = useReducer(reducer, INITIAL_STATE);\n  return (\n    <PortalDispatchContext.Provider value={dispatch}>\n      <PortalStateContext.Provider value={state}>\n        {children}\n        {shouldAddRootHost && <PortalHost name={rootHostName} />}\n      </PortalStateContext.Provider>\n    </PortalDispatchContext.Provider>\n  );\n};\n\nexport const PortalProvider = memo(PortalProviderComponent);\nPortalProvider.displayName = 'PortalProvider';\n"]}