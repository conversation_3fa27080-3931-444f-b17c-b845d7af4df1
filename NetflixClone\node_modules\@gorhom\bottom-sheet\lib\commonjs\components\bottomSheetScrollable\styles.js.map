{"version": 3, "names": ["_reactNative", "require", "styles", "exports", "StyleSheet", "create", "container", "flex", "overflow"], "sourceRoot": "../../../../src", "sources": ["components/bottomSheetScrollable/styles.ts"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEO,MAAMC,MAAM,GAAAC,OAAA,CAAAD,MAAA,GAAGE,uBAAU,CAACC,MAAM,CAAC;EACtCC,SAAS,EAAE;IACTC,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC", "ignoreList": []}