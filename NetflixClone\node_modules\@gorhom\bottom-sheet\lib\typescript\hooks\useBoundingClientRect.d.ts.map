{"version": 3, "file": "useBoundingClientRect.d.ts", "sourceRoot": "", "sources": ["../../../src/hooks/useBoundingClientRect.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,SAAS,EAAmB,MAAM,OAAO,CAAC;AACxD,OAAO,KAAK,EAAE,IAAI,EAAE,MAAM,cAAc,CAAC;AAGzC,MAAM,MAAM,kBAAkB,GAAG;IAC/B,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE,MAAM,CAAC;IACd,GAAG,EAAE,MAAM,CAAC;IACZ,MAAM,EAAE,MAAM,CAAC;CAChB,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AACH,wBAAgB,qBAAqB,CACnC,GAAG,EAAE,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,EAC3B,OAAO,EAAE,CAAC,MAAM,EAAE,kBAAkB,KAAK,IAAI,QA2B9C"}