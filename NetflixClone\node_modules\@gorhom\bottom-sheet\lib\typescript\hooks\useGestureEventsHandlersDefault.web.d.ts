import type { GestureEventHandlerCallbackType } from '../types';
export declare const useGestureEventsHandlersDefault: () => {
    handleOnStart: GestureEventHandlerCallbackType;
    handleOnChange: GestureEventHandlerCallbackType;
    handleOnEnd: GestureEventHandlerCallbackType;
    handleOnFinalize: GestureEventHandlerCallbackType;
};
//# sourceMappingURL=useGestureEventsHandlersDefault.web.d.ts.map