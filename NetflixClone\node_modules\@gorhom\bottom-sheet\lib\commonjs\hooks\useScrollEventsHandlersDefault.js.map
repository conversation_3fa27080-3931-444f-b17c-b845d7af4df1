{"version": 3, "names": ["_reactNativeGestureHandler", "require", "_reactNativeReanimated", "_constants", "_useBottomSheetInternal", "useScrollEventsHandlersDefault", "scrollableRef", "scrollableContentOffsetY", "animatedSheetState", "animatedScrollableState", "animatedAnimationState", "animatedHandleGestureState", "animatedScrollableContentOffsetY", "rootScrollableContentOffsetY", "useBottomSheetInternal", "handleOnScroll", "useWorkletCallback", "contentOffset", "y", "context", "value", "SHEET_STATE", "EXTENDED", "FILL_PARENT", "shouldLockInitialPosition", "State", "ACTIVE", "initialContentOffsetY", "SCROLLABLE_STATE", "LOCKED", "lockPosition", "scrollTo", "handleOnBeginDrag", "handleOnEndDrag", "ANIMATION_STATE", "RUNNING", "handleOnMomentumEnd", "exports"], "sourceRoot": "../../../src", "sources": ["hooks/useScrollEventsHandlersDefault.ts"], "mappings": ";;;;;;AAAA,IAAAA,0BAAA,GAAAC,OAAA;AACA,IAAAC,sBAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AAKA,IAAAG,uBAAA,GAAAH,OAAA;AAOO,MAAMI,8BAA4D,GAAGA,CAC1EC,aAAa,EACbC,wBAAwB,KACrB;EACH;EACA,MAAM;IACJC,kBAAkB;IAClBC,uBAAuB;IACvBC,sBAAsB;IACtBC,0BAA0B;IAC1BC,gCAAgC,EAAEC;EACpC,CAAC,GAAG,IAAAC,8CAAsB,EAAC,CAAC;;EAE5B;EACA,MAAMC,cAAsE,GAC1E,IAAAC,yCAAkB,EAChB,CAAC;IAAEC,aAAa,EAAE;MAAEC;IAAE;EAAE,CAAC,EAAEC,OAAO,KAAK;IACrC;AACR;AACA;AACA;IACQ,IACEX,kBAAkB,CAACY,KAAK,KAAKC,sBAAW,CAACC,QAAQ,IACjDd,kBAAkB,CAACY,KAAK,KAAKC,sBAAW,CAACE,WAAW,EACpD;MACAJ,OAAO,CAACK,yBAAyB,GAAG,KAAK;IAC3C;;IAEA;AACR;AACA;AACA;IACQ,IAAIb,0BAA0B,CAACS,KAAK,KAAKK,gCAAK,CAACC,MAAM,EAAE;MACrDP,OAAO,CAACK,yBAAyB,GAAG,IAAI;MACxCL,OAAO,CAACQ,qBAAqB,GAAGT,CAAC;IACnC;IAEA,IAAIT,uBAAuB,CAACW,KAAK,KAAKQ,2BAAgB,CAACC,MAAM,EAAE;MAC7D,MAAMC,YAAY,GAAGX,OAAO,CAACK,yBAAyB,GACjDL,OAAO,CAACQ,qBAAqB,IAAI,CAAC,GACnC,CAAC;MACL;MACA,IAAAI,+BAAQ,EAACzB,aAAa,EAAE,CAAC,EAAEwB,YAAY,EAAE,KAAK,CAAC;MAC/CvB,wBAAwB,CAACa,KAAK,GAAGU,YAAY;MAC7C;IACF;EACF,CAAC,EACD,CACExB,aAAa,EACbC,wBAAwB,EACxBE,uBAAuB,EACvBD,kBAAkB,CAEtB,CAAC;EACH,MAAMwB,iBAAyE,GAC7E,IAAAhB,yCAAkB,EAChB,CAAC;IAAEC,aAAa,EAAE;MAAEC;IAAE;EAAE,CAAC,EAAEC,OAAO,KAAK;IACrCZ,wBAAwB,CAACa,KAAK,GAAGF,CAAC;IAClCL,4BAA4B,CAACO,KAAK,GAAGF,CAAC;IACtCC,OAAO,CAACQ,qBAAqB,GAAGT,CAAC;;IAEjC;AACR;AACA;AACA;IACQ,IACEV,kBAAkB,CAACY,KAAK,KAAKC,sBAAW,CAACC,QAAQ,IACjDd,kBAAkB,CAACY,KAAK,KAAKC,sBAAW,CAACE,WAAW,IACpDL,CAAC,GAAG,CAAC,EACL;MACAC,OAAO,CAACK,yBAAyB,GAAG,IAAI;IAC1C,CAAC,MAAM;MACLL,OAAO,CAACK,yBAAyB,GAAG,KAAK;IAC3C;EACF,CAAC,EACD,CACEjB,wBAAwB,EACxBC,kBAAkB,EAClBK,4BAA4B,CAEhC,CAAC;EACH,MAAMoB,eAAuE,GAC3E,IAAAjB,yCAAkB,EAChB,CAAC;IAAEC,aAAa,EAAE;MAAEC;IAAE;EAAE,CAAC,EAAEC,OAAO,KAAK;IACrC,IAAIV,uBAAuB,CAACW,KAAK,KAAKQ,2BAAgB,CAACC,MAAM,EAAE;MAC7D,MAAMC,YAAY,GAAGX,OAAO,CAACK,yBAAyB,GACjDL,OAAO,CAACQ,qBAAqB,IAAI,CAAC,GACnC,CAAC;MACL;MACA,IAAAI,+BAAQ,EAACzB,aAAa,EAAE,CAAC,EAAEwB,YAAY,EAAE,KAAK,CAAC;MAC/CvB,wBAAwB,CAACa,KAAK,GAAGU,YAAY;MAC7C;IACF;IAEA,IAAIpB,sBAAsB,CAACU,KAAK,KAAKc,0BAAe,CAACC,OAAO,EAAE;MAC5D5B,wBAAwB,CAACa,KAAK,GAAGF,CAAC;MAClCL,4BAA4B,CAACO,KAAK,GAAGF,CAAC;IACxC;EACF,CAAC,EACD,CACEZ,aAAa,EACbC,wBAAwB,EACxBG,sBAAsB,EACtBD,uBAAuB,EACvBI,4BAA4B,CAEhC,CAAC;EACH,MAAMuB,mBAA2E,GAC/E,IAAApB,yCAAkB,EAChB,CAAC;IAAEC,aAAa,EAAE;MAAEC;IAAE;EAAE,CAAC,EAAEC,OAAO,KAAK;IACrC,IAAIV,uBAAuB,CAACW,KAAK,KAAKQ,2BAAgB,CAACC,MAAM,EAAE;MAC7D,MAAMC,YAAY,GAAGX,OAAO,CAACK,yBAAyB,GACjDL,OAAO,CAACQ,qBAAqB,IAAI,CAAC,GACnC,CAAC;MACL;MACA,IAAAI,+BAAQ,EAACzB,aAAa,EAAE,CAAC,EAAEwB,YAAY,EAAE,KAAK,CAAC;MAC/CvB,wBAAwB,CAACa,KAAK,GAAG,CAAC;MAClC;IACF;IAEA,IAAIV,sBAAsB,CAACU,KAAK,KAAKc,0BAAe,CAACC,OAAO,EAAE;MAC5D5B,wBAAwB,CAACa,KAAK,GAAGF,CAAC;MAClCL,4BAA4B,CAACO,KAAK,GAAGF,CAAC;IACxC;EACF,CAAC,EACD,CACEX,wBAAwB,EACxBD,aAAa,EACbI,sBAAsB,EACtBD,uBAAuB,EACvBI,4BAA4B,CAEhC,CAAC;EACH;;EAEA,OAAO;IACLE,cAAc;IACdiB,iBAAiB;IACjBC,eAAe;IACfG;EACF,CAAC;AACH,CAAC;AAACC,OAAA,CAAAhC,8BAAA,GAAAA,8BAAA", "ignoreList": []}