{"name": "@gorhom/bottom-sheet", "version": "5.1.6", "description": "A performant interactive bottom sheet with fully configurable options 🚀", "main": "lib/commonjs/index", "module": "lib/module/index", "types": "lib/typescript/index.d.ts", "react-native": "src/index.ts", "source": "src/index.ts", "files": ["src", "lib", "mock.js"], "keywords": ["react-native", "ios", "android", "web", "bottom-sheet", "bottomsheet", "reanimated", "sheet"], "repository": "https://github.com/gorhom/react-native-bottom-sheet", "author": "<PERSON> (https://gorhom.dev)", "license": "MIT", "bugs": {"url": "https://github.com/gorhom/react-native-bottom-sheet/issues"}, "homepage": "https://gorhom.dev/react-native-bottom-sheet/", "scripts": {"typescript": "tsc --skip<PERSON><PERSON><PERSON><PERSON><PERSON> --noEmit", "lint": "biome lint --error-on-warnings ./src", "build": "bob build && yarn copy-dts && yarn delete-dts.js && yarn delete-debug-view", "copy-dts": "copyfiles -u 1 \"src/**/*.d.ts\" lib/typescript", "delete-debug-view": "rm -r ./lib/commonjs/components/bottomSheetDebugView && rm -r ./lib/module/components/bottomSheetDebugView && rm -r ./lib/typescript/components/bottomSheetDebugView", "delete-dts.js": "find ./lib/commonjs -name '*.d.js*' -delete && find ./lib/module -name '*.d.js*' -delete", "release": "rm -rf lib && yarn build && release-it", "example": "yarn --cwd example", "bootstrap": "yarn install && yarn example"}, "dependencies": {"@gorhom/portal": "1.0.14", "invariant": "^2.2.4"}, "devDependencies": {"@commitlint/cli": "^17.6.5", "@commitlint/config-conventional": "^17.6.5", "@release-it/conventional-changelog": "^8.0.1", "@types/invariant": "^2.2.34", "@types/react": "~18.3.12", "@types/react-native": "~0.73.0", "copyfiles": "^2.4.1", "husky": "^4.3.8", "lint-staged": "^13.2.2", "metro-react-native-babel-preset": "^0.77.0", "react": "18.3.1", "react-native": "0.76.0", "react-native-builder-bob": "^0.30.3", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "release-it": "^17.6.0", "typescript": "^5.3.0"}, "peerDependencies": {"@types/react": "*", "@types/react-native": "*", "react": "*", "react-native": "*", "react-native-gesture-handler": ">=2.16.1", "react-native-reanimated": ">=3.16.0"}, "peerDependenciesMeta": {"@types/react-native": {"optional": true}, "@types/react": {"optional": true}}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", "typescript"]}}