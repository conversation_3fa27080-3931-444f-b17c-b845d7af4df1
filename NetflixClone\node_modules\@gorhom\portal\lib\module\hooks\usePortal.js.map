{"version": 3, "sources": ["usePortal.ts"], "names": ["useCallback", "useContext", "ACTIONS", "PortalDispatchContext", "usePortal", "hostName", "dispatch", "Error", "registerHost", "type", "REGISTER_HOST", "deregisterHost", "DEREGISTER_HOST", "addUpdatePortal", "name", "node", "ADD_UPDATE_PORTAL", "portalName", "<PERSON><PERSON><PERSON><PERSON>", "REMOVE_PORTAL", "addPortal", "updatePortal"], "mappings": "AAAA,SAAoBA,WAApB,EAAiCC,UAAjC,QAAmD,OAAnD;AACA,SAASC,OAAT,QAAwB,oBAAxB;AACA,SAASC,qBAAT,QAAsC,oBAAtC;AAEA,OAAO,MAAMC,SAAS,GAAG,CAACC,QAAgB,GAAG,MAApB,KAA+B;AACtD,QAAMC,QAAQ,GAAGL,UAAU,CAACE,qBAAD,CAA3B;;AAEA,MAAIG,QAAQ,KAAK,IAAjB,EAAuB;AACrB,UAAM,IAAIC,KAAJ,CACJ,4FADI,CAAN;AAGD,GAPqD,CAStD;;;AACA,QAAMC,YAAY,GAAGR,WAAW,CAAC,MAAM;AACrCM,IAAAA,QAAQ,CAAC;AACPG,MAAAA,IAAI,EAAEP,OAAO,CAACQ,aADP;AAEPL,MAAAA,QAAQ,EAAEA;AAFH,KAAD,CAAR,CADqC,CAKrC;AACD,GAN+B,EAM7B,EAN6B,CAAhC;AAQA,QAAMM,cAAc,GAAGX,WAAW,CAAC,MAAM;AACvCM,IAAAA,QAAQ,CAAC;AACPG,MAAAA,IAAI,EAAEP,OAAO,CAACU,eADP;AAEPP,MAAAA,QAAQ,EAAEA;AAFH,KAAD,CAAR,CADuC,CAKvC;AACD,GANiC,EAM/B,EAN+B,CAAlC;AAQA,QAAMQ,eAAe,GAAGb,WAAW,CAAC,CAACc,IAAD,EAAeC,IAAf,KAAmC;AACrET,IAAAA,QAAQ,CAAC;AACPG,MAAAA,IAAI,EAAEP,OAAO,CAACc,iBADP;AAEPX,MAAAA,QAFO;AAGPY,MAAAA,UAAU,EAAEH,IAHL;AAIPC,MAAAA;AAJO,KAAD,CAAR,CADqE,CAOrE;AACD,GARkC,EAQhC,EARgC,CAAnC;AAUA,QAAMG,YAAY,GAAGlB,WAAW,CAAEc,IAAD,IAAkB;AACjDR,IAAAA,QAAQ,CAAC;AACPG,MAAAA,IAAI,EAAEP,OAAO,CAACiB,aADP;AAEPd,MAAAA,QAFO;AAGPY,MAAAA,UAAU,EAAEH;AAHL,KAAD,CAAR,CADiD,CAMjD;AACD,GAP+B,EAO7B,EAP6B,CAAhC,CApCsD,CA4CtD;;AAEA,SAAO;AACLN,IAAAA,YADK;AAELG,IAAAA,cAFK;AAGLS,IAAAA,SAAS,EAAEP,eAHN;AAILQ,IAAAA,YAAY,EAAER,eAJT;AAKLK,IAAAA;AALK,GAAP;AAOD,CArDM", "sourcesContent": ["import { ReactNode, useCallback, useContext } from 'react';\nimport { ACTIONS } from '../state/constants';\nimport { PortalDispatchContext } from '../contexts/portal';\n\nexport const usePortal = (hostName: string = 'root') => {\n  const dispatch = useContext(PortalDispatchContext);\n\n  if (dispatch === null) {\n    throw new Error(\n      \"'PortalDispatchContext' cannot be null, please add 'PortalProvider' to the root component.\"\n    );\n  }\n\n  //#region methods\n  const registerHost = useCallback(() => {\n    dispatch({\n      type: ACTIONS.REGISTER_HOST,\n      hostName: hostName,\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  const deregisterHost = useCallback(() => {\n    dispatch({\n      type: ACTIONS.DEREGISTER_HOST,\n      hostName: hostName,\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  const addUpdatePortal = useCallback((name: string, node: ReactNode) => {\n    dispatch({\n      type: ACTIONS.ADD_UPDATE_PORTAL,\n      hostName,\n      portalName: name,\n      node,\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  const removePortal = useCallback((name: string) => {\n    dispatch({\n      type: ACTIONS.REMOVE_PORTAL,\n      hostName,\n      portalName: name,\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  //#endregion\n\n  return {\n    registerHost,\n    deregisterHost,\n    addPortal: addUpdatePortal,\n    updatePortal: addUpdatePortal,\n    removePortal,\n  };\n};\n"]}