{"version": 3, "names": ["useCallback", "useBottomSheetInternal", "useBottomSheetContentSizeSetter", "enableDynamicSizing", "animatedContentHeight", "setContentSize", "contentHeight", "set"], "sourceRoot": "../../../../src", "sources": ["components/bottomSheetScrollable/useBottomSheetContentSizeSetter.ts"], "mappings": ";;AAAA,SAASA,WAAW,QAAQ,OAAO;AACnC,SAASC,sBAAsB,QAAQ,aAAa;;AAEpD;AACA;AACA;AACA;AACA,OAAO,SAASC,+BAA+BA,CAAA,EAAG;EAChD;EACA,MAAM;IAAEC,mBAAmB;IAAEC;EAAsB,CAAC,GAClDH,sBAAsB,CAAC,CAAC;EAC1B;;EAEA;EACA,MAAMI,cAAc,GAAGL,WAAW,CAC/BM,aAAqB,IAAK;IACzB,IAAI,CAACH,mBAAmB,EAAE;MACxB;IACF;IACAC,qBAAqB,CAACG,GAAG,CAACD,aAAa,CAAC;EAC1C,CAAC,EACD,CAACH,mBAAmB,EAAEC,qBAAqB,CAC7C,CAAC;EACD;;EAEA,OAAO;IACLC;EACF,CAAC;AACH", "ignoreList": []}