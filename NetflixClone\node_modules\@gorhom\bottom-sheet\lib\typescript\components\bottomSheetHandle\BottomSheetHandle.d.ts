import React from 'react';
import type { BottomSheetDefaultHandleProps } from './types';
declare function BottomSheetHandleComponent({ style, indicatorStyle: _indicatorStyle, accessible, accessibilityRole, accessibilityLabel, accessibilityHint, children, }: BottomSheetDefaultHandleProps): React.JSX.Element;
declare const BottomSheetHandle: React.MemoExoticComponent<typeof BottomSheetHandleComponent>;
export default BottomSheetHandle;
//# sourceMappingURL=BottomSheetHandle.d.ts.map