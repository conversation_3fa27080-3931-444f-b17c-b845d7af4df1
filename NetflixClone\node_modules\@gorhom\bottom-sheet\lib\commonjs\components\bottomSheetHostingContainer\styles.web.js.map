{"version": 3, "names": ["_reactNative", "require", "styles", "exports", "StyleSheet", "create", "container", "position", "left", "right", "bottom", "top"], "sourceRoot": "../../../../src", "sources": ["components/bottomSheetHostingContainer/styles.web.ts"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEO,MAAMC,MAAM,GAAAC,OAAA,CAAAD,MAAA,GAAGE,uBAAU,CAACC,MAAM,CAAC;EACtCC,SAAS,EAAE;IACTC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,GAAG,EAAE;EACP;AACF,CAAC,CAAC", "ignoreList": []}